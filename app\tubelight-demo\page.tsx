'use client'

import { InnoHubTubeLightNavBar } from '@/components/ui/innohub-tubelight-navbar'
import { NavBarDemo, InnoHubNavBarDemo } from '@/components/ui/tubelight-navbar-demo'

export default function TubeLightDemoPage() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Replace the default navbar with tubelight navbar */}
      <InnoHubTubeLightNavBar />
      
      <div className="pt-24 px-4 max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            TubeLight Navbar Demo
          </h1>
          <p className="text-gray-300 text-lg">
            Experience the new tubelight navbar design for InnoHub
          </p>
        </div>

        <div className="space-y-8">
          <div className="bg-gray-900/50 rounded-lg p-6 border border-purple-500/20">
            <h2 className="text-2xl font-semibold mb-4 text-purple-400">Features</h2>
            <ul className="space-y-2 text-gray-300">
              <li>✨ Smooth tubelight animation effect on active navigation items</li>
              <li>🎨 Consistent with InnoHub's dark/purple theme</li>
              <li>📱 Responsive design with mobile navigation</li>
              <li>🌐 Integrated language switcher</li>
              <li>🔗 Maintains all existing functionality</li>
              <li>⚡ Powered by Framer Motion for smooth animations</li>
            </ul>
          </div>

          <div className="bg-gray-900/50 rounded-lg p-6 border border-purple-500/20">
            <h2 className="text-2xl font-semibold mb-4 text-purple-400">How to Use</h2>
            <div className="space-y-4 text-gray-300">
              <p>
                The tubelight navbar is now integrated with your existing InnoHub navigation system.
                It includes:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>All your existing navigation links</li>
                <li>Language switching functionality</li>
                <li>Courses button</li>
                <li>Mobile responsive design</li>
                <li>Scroll-based background changes</li>
              </ul>
            </div>
          </div>

          <div className="bg-gray-900/50 rounded-lg p-6 border border-purple-500/20">
            <h2 className="text-2xl font-semibold mb-4 text-purple-400">Implementation</h2>
            <div className="space-y-4 text-gray-300">
              <p>
                To replace your current navbar with the tubelight version:
              </p>
              <ol className="list-decimal list-inside space-y-2 ml-4">
                <li>Import the InnoHubTubeLightNavBar component</li>
                <li>Replace the existing Navbar component in your layout</li>
                <li>The component automatically handles active states and animations</li>
              </ol>
            </div>
          </div>

          {/* Demo sections with content to show scrolling effect */}
          <div className="space-y-12 mt-16">
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="bg-gray-900/30 rounded-lg p-8 border border-purple-500/10">
                <h3 className="text-xl font-semibold mb-4 text-purple-300">
                  Demo Section {i + 1}
                </h3>
                <p className="text-gray-400 leading-relaxed">
                  This is demo content to showcase the navbar behavior during scrolling. 
                  Notice how the navbar background changes when you scroll down, maintaining 
                  the tubelight effect while adapting to the scroll state. The active navigation 
                  item is highlighted with a beautiful tubelight animation that follows your 
                  current page location.
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
