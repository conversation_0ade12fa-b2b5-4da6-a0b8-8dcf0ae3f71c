"use client";

import { useEffect, useRef } from "react";

export function useTimelineImageEffect() {
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    // Function to handle the intersection observer callback
    const handleIntersect = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        // Get the image wrapper element
        const imageWrapper = entry.target.querySelector(".timeline-image-wrapper");
        
        if (!imageWrapper) return;
        
        // Calculate how far through the viewport the element is
        const viewportHeight = window.innerHeight;
        const elementTop = entry.boundingClientRect.top;
        const elementHeight = entry.boundingClientRect.height;
        const position = elementTop / (viewportHeight - elementHeight);
        
        // Apply different rotation classes based on position in viewport
        if (entry.isIntersecting) {
          if (position < 0.3) {
            // Element is in the top portion of the viewport
            imageWrapper.classList.add("rotate-in");
            imageWrapper.classList.remove("rotate-out");
          } else if (position > 0.100) {
            // Element is in the bottom portion of the viewport
            imageWrapper.classList.add("rotate-out");
            imageWrapper.classList.remove("rotate-in");
          } else {
            // Element is in the middle of the viewport
            imageWrapper.classList.remove("rotate-in");
            imageWrapper.classList.remove("rotate-out");
          }
        } else {
          // Reset when not in viewport
          imageWrapper.classList.remove("rotate-in");
          imageWrapper.classList.remove("rotate-out");
        }
      });
    };

    // Set up the intersection observer
    observerRef.current = new IntersectionObserver(handleIntersect, {
      root: null, // Use the viewport
      rootMargin: "0px",
      threshold: [0, 0.25, 0.5, 0.75, 1] // Multiple thresholds for smoother transitions
    });

    // Select all timeline image containers and observe them
    const imageContainers = document.querySelectorAll(".timeline-image-container");
    imageContainers.forEach((container) => {
      observerRef.current?.observe(container);
    });

    // Clean up the observer on component unmount
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return null;
}

export default function TimelineImageEffect() {
  useTimelineImageEffect();
  return null; // This component doesn't render anything
}
