"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface AuthCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

interface AuthCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

interface AuthCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

interface AuthCardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

interface AuthCardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
}

interface AuthCardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

const AuthCard = React.forwardRef<HTMLDivElement, AuthCardProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn("relative group", className)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: [0.19, 1, 0.22, 1] }}
        {...props}
      >
        {/* Animated gradient border */}
        <motion.div
          className="absolute -inset-0.5 bg-gradient-to-r from-primary/30 via-purple-500/30 to-primary/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"
          animate={{
            backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{
            backgroundSize: "200% 100%",
          }}
        />
        
        {/* Main card */}
        <div className="relative bg-black/80 backdrop-blur-xl border border-primary/20 rounded-xl overflow-hidden">
          {/* Subtle inner glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-purple-500/5 pointer-events-none" />
          
          {/* Content */}
          <div className="relative z-10">
            {children}
          </div>
        </div>
      </motion.div>
    );
  }
);

const AuthCardHeader = React.forwardRef<HTMLDivElement, AuthCardHeaderProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn("flex flex-col space-y-2 p-6 sm:p-8 pb-4 text-center", className)}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1, ease: [0.19, 1, 0.22, 1] }}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

const AuthCardContent = React.forwardRef<HTMLDivElement, AuthCardContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn("px-6 sm:px-8 pb-4", className)}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2, ease: [0.19, 1, 0.22, 1] }}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

const AuthCardFooter = React.forwardRef<HTMLDivElement, AuthCardFooterProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn("flex items-center justify-center p-6 sm:p-8 pt-4", className)}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3, ease: [0.19, 1, 0.22, 1] }}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

const AuthCardTitle = React.forwardRef<HTMLHeadingElement, AuthCardTitleProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <h2
        ref={ref}
        className={cn("text-2xl sm:text-3xl font-bold tracking-tight text-white", className)}
        {...props}
      >
        {children}
      </h2>
    );
  }
);

const AuthCardDescription = React.forwardRef<HTMLParagraphElement, AuthCardDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn("text-muted-foreground text-sm sm:text-base leading-relaxed", className)}
        {...props}
      >
        {children}
      </p>
    );
  }
);

AuthCard.displayName = "AuthCard";
AuthCardHeader.displayName = "AuthCardHeader";
AuthCardContent.displayName = "AuthCardContent";
AuthCardFooter.displayName = "AuthCardFooter";
AuthCardTitle.displayName = "AuthCardTitle";
AuthCardDescription.displayName = "AuthCardDescription";

export {
  AuthCard,
  AuthCardHeader,
  AuthCardContent,
  AuthCardFooter,
  AuthCardTitle,
  AuthCardDescription,
};
