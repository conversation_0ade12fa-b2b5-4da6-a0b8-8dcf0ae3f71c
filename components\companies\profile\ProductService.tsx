'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Badge } from '@/components/ui/badge';
import { Rocket, Settings, Users, Star } from 'lucide-react';

interface ProductServiceProps {
  company: Company;
}

export function ProductService({ company }: ProductServiceProps) {
  return (
    <div className="space-y-6">
      {/* Product Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/20 rounded-lg">
                <Rocket className="h-5 w-5 text-primary" />
              </div>
              <AuthCardTitle>Product Overview</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              {company.description}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-primary/5 border border-primary/20 rounded-lg">
                <Settings className="h-8 w-8 text-primary mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Core Technology</h4>
                <p className="text-sm text-muted-foreground">IoT & AI Integration</p>
              </div>
              <div className="text-center p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
                <Users className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Target Users</h4>
                <p className="text-sm text-muted-foreground">Commercial Farmers</p>
              </div>
              <div className="text-center p-4 bg-yellow-500/5 border border-yellow-500/20 rounded-lg">
                <Star className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Key Benefit</h4>
                <p className="text-sm text-muted-foreground">40% Yield Increase</p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Key Features */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Key Features</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { name: 'Smart Irrigation', description: 'Automated watering based on soil moisture and weather data', icon: '💧' },
                { name: 'Crop Monitoring', description: 'Real-time monitoring of crop health and growth patterns', icon: '🌱' },
                { name: 'Weather Integration', description: 'Advanced weather forecasting and climate adaptation', icon: '🌤️' },
                { name: 'Data Analytics', description: 'AI-powered insights and predictive analytics dashboard', icon: '📊' },
                { name: 'Mobile App', description: 'User-friendly mobile application for remote monitoring', icon: '📱' },
                { name: 'Alert System', description: 'Instant notifications for critical farming conditions', icon: '🔔' }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex items-start gap-3 p-4 bg-primary/5 border border-primary/20 rounded-lg"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 + index * 0.1 }}
                >
                  <span className="text-2xl">{feature.icon}</span>
                  <div>
                    <h4 className="font-medium text-white mb-1">{feature.name}</h4>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Use Cases */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Use Cases</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="space-y-4">
              {[
                'Large-scale grain production optimization',
                'Greenhouse vegetable farming automation',
                'Fruit orchard management and monitoring',
                'Sustainable farming practice implementation',
                'Water conservation in arid regions'
              ].map((useCase, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-3 p-3 bg-green-500/5 border border-green-500/20 rounded-lg"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                >
                  <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0" />
                  <p className="text-sm text-muted-foreground">{useCase}</p>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Customer Testimonials */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Customer Testimonials</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  quote: "EcoGrow has transformed our farming operations. We've seen a 35% increase in yield while reducing water usage significantly.",
                  customer: "Munkh-Erdene",
                  company: "Golden Valley Farms",
                  role: "Farm Manager"
                },
                {
                  quote: "The real-time monitoring and automated irrigation have saved us countless hours and improved our crop quality dramatically.",
                  customer: "Batbayar",
                  company: "Green Fields Cooperative",
                  role: "Agricultural Director"
                }
              ].map((testimonial, index) => (
                <motion.div
                  key={index}
                  className="p-4 bg-primary/5 border border-primary/20 rounded-lg"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                >
                  <p className="text-muted-foreground italic mb-3">"{testimonial.quote}"</p>
                  <div className="flex items-center gap-2">
                    <div>
                      <p className="text-sm font-medium text-white">{testimonial.customer}</p>
                      <p className="text-xs text-muted-foreground">{testimonial.role} at {testimonial.company}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
