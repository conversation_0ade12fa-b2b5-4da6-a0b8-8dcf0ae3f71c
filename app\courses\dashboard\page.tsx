'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  Clock, 
  Award, 
  TrendingUp, 
  User,
  LogOut,
  Play,
  Star,
  ChevronRight,
  Target,
  Calendar
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { getCurrentUser, logout } from '@/lib/auth';
import ProtectedRoute from '@/components/courses/ProtectedRoute';
import CoursesNavbar from '@/components/courses/CoursesNavbar';

// Course data
const FEATURED_COURSES = [
  {
    id: 1,
    title: 'Entrepreneurship Fundamentals',
    description: 'Learn the fundamentals of starting and growing a successful business',
    thumbnail: 'hlCYcATi0m4',
    duration: '1h 29m',
    lessons: 8,
    progress: 75,
    instructor: 'Otgonbaatar Tsedendemberel',
    category: 'Business'
  },
  {
    id: 2,
    title: 'Digital Marketing for Startups',
    description: 'Master digital marketing strategies to grow your startup on a budget',
    thumbnail: 'gElfIo6uw4g',
    duration: '2h 15m',
    lessons: 12,
    progress: 25,
    instructor: 'Sarah Chen',
    category: 'Marketing'
  },
  {
    id: 3,
    title: 'Startup Funding Masterclass',
    description: 'Complete guide to raising capital and securing investment',
    thumbnail: 'dQw4w9WgXcQ',
    duration: '18m',
    lessons: 5,
    progress: 100,
    instructor: 'Investment Experts',
    category: 'Finance'
  }
];

export default function CoursesDashboard() {
  const { t, isLoaded } = useLanguage();
  const [user, setUser] = useState<any>(null);
  const [stats, setStats] = useState({
    coursesStarted: 4,
    coursesCompleted: 1,
    totalHours: 24,
    streak: 7
  });

  useEffect(() => {
    const currentUser = getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
    }
  }, []);

  const handleLogout = () => {
    logout();
    window.location.href = '/';
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-black via-purple-900/20 to-black">
        <CoursesNavbar />

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Welcome Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">
                Welcome back, {user?.name || 'Learner'}!
              </h1>
              <p className="text-xl text-gray-400">
                Continue your entrepreneurial journey with personalized learning
              </p>
            </div>
          </motion.div>

          {/* Stats Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <BookOpen className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.coursesStarted}</div>
                <div className="text-sm text-gray-400">Courses Started</div>
              </CardContent>
            </Card>
            
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Award className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.coursesCompleted}</div>
                <div className="text-sm text-gray-400">Completed</div>
              </CardContent>
            </Card>

            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Clock className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.totalHours}h</div>
                <div className="text-sm text-gray-400">Hours Learned</div>
              </CardContent>
            </Card>

            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Target className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stats.streak}</div>
                <div className="text-sm text-gray-400">Day Streak</div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Continue Learning Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="mb-8"
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-purple-500" />
                    Continue Learning
                  </CardTitle>
                  <Link href="/courses/catalog">
                    <Button variant="outline" size="sm" className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10">
                      View All Courses
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {FEATURED_COURSES.map((course) => (
                    <div key={course.id} className="group cursor-pointer">
                      <Card className="border-purple-500/20 bg-black/40 hover:border-purple-500/40 transition-all duration-300">
                        {/* Course Thumbnail */}
                        <div className="relative aspect-video overflow-hidden rounded-t-lg">
                          <Image
                            src={`https://img.youtube.com/vi/${course.thumbnail}/maxresdefault.jpg`}
                            alt={course.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = 'https://placehold.co/600x400/3a0647/ffffff?text=InnoHub+Course';
                            }}
                          />
                          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <Play className="h-12 w-12 text-white" />
                          </div>
                          
                          {/* Progress Badge */}
                          {course.progress > 0 && (
                            <div className="absolute top-3 right-3 bg-black/70 text-white text-xs px-2 py-1 rounded">
                              {course.progress}% Complete
                            </div>
                          )}
                        </div>

                        <CardContent className="p-4">
                          <Badge className="bg-purple-500/20 text-purple-300 text-xs mb-2">
                            {course.category}
                          </Badge>
                          
                          <h3 className="font-semibold text-white mb-2 group-hover:text-purple-400 transition-colors">
                            {course.title}
                          </h3>
                          
                          <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                            {course.description}
                          </p>

                          <div className="flex items-center justify-between text-sm text-gray-400 mb-3">
                            <span>{course.duration}</span>
                            <span>{course.lessons} lessons</span>
                          </div>

                          {course.progress > 0 && (
                            <div className="mb-3">
                              <Progress value={course.progress} className="h-2" />
                            </div>
                          )}

                          <Button 
                            className="w-full bg-purple-500/20 text-purple-400 hover:bg-purple-500 hover:text-white border-purple-500/30"
                            onClick={() => window.location.href = `/courses/${course.id}`}
                          >
                            {course.progress > 0 ? 'Continue Learning' : 'Start Course'}
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
