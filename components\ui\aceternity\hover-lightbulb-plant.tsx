'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export const HoverLightbulbPlant = ({
  className,
}: {
  className?: string;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isActivated, setIsActivated] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Flower colors
  const flowerColors = [
    'bg-pink-400',
    'bg-purple-400',
    'bg-blue-400',
    'bg-red-400',
    'bg-yellow-400',
  ];

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setMousePosition({ x, y });

    // Calculate distance from center of lightbulb
    const centerX = rect.width / 2;
    const centerY = rect.height / 3; // Lightbulb is positioned at top 1/3
    const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));

    // Activate when cursor is close to the center of the lightbulb
    setIsActivated(distance < 40);
  };

  const handleMouseLeave = () => {
    setIsActivated(false);
  };

  return (
    <div
      ref={containerRef}
      className={cn('relative w-full max-w-md mx-auto aspect-square cursor-pointer', className)}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {/* Glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full bg-yellow-400/20 blur-2xl"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{
          scale: isActivated ? 1.5 : 1,
          opacity: isActivated ? 0.8 : 0.2
        }}
        transition={{
          duration: 0.8,
          ease: "easeInOut"
        }}
      />

      {/* Lightbulb */}
      <motion.div
        className="absolute top-1/4 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32"
        initial={{ y: 0, opacity: 1 }}
        animate={{
          y: isActivated ? -5 : 0,
          scale: isActivated ? 1.1 : 1
        }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative w-full h-full">
          {/* Bulb glass */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-b from-yellow-100/90 to-yellow-300/90 rounded-full backdrop-blur-sm border border-white/30"
            animate={{
              boxShadow: isActivated
                ? '0 0 25px 8px rgba(255, 215, 0, 0.8)'
                : '0 0 5px 2px rgba(255, 215, 0, 0.3)'
            }}
            transition={{ duration: 0.5 }}
          />

          {/* Bulb reflection */}
          <motion.div
            className="absolute top-1/4 left-1/4 w-1/4 h-1/4 bg-white/60 rounded-full blur-[1px]"
            animate={{
              opacity: isActivated ? 0.8 : 0.4
            }}
            transition={{ duration: 0.5 }}
          />

          {/* Filament */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-12">
            <motion.div
              className="w-[2px] h-8 bg-yellow-500 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              initial={{ opacity: 0.2 }}
              animate={{
                opacity: isActivated ? [0.7, 1, 0.7] : [0.2, 0.5, 0.2],
              }}
              transition={{
                duration: isActivated ? 0.2 : 0.8,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            />
            <motion.div
              className="w-[2px] h-8 bg-yellow-500 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-90"
              initial={{ opacity: 0.2 }}
              animate={{
                opacity: isActivated ? [0.7, 1, 0.7] : [0.2, 0.5, 0.2],
              }}
              transition={{
                duration: isActivated ? 0.3 : 1,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            />
            <motion.div
              className="w-[2px] h-8 bg-yellow-500 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-45"
              initial={{ opacity: 0 }}
              animate={{
                opacity: isActivated ? [0.7, 1, 0.7] : 0,
              }}
              transition={{
                duration: 0.25,
                repeat: isActivated ? Infinity : 0,
                repeatType: 'reverse',
              }}
            />
            <motion.div
              className="w-[2px] h-8 bg-yellow-500 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 -rotate-45"
              initial={{ opacity: 0 }}
              animate={{
                opacity: isActivated ? [0.7, 1, 0.7] : 0,
              }}
              transition={{
                duration: 0.35,
                repeat: isActivated ? Infinity : 0,
                repeatType: 'reverse',
              }}
            />
          </div>

          {/* Base */}
          <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-10 h-4 bg-gray-300 rounded-md" />
        </div>
      </motion.div>

      {/* Main stem */}
      <motion.div
        className="absolute top-1/4 left-1/2 -translate-x-1/2 w-1.5 bg-green-500"
        style={{ top: '40%' }}
        initial={{ height: '0%' }}
        animate={{
          height: isActivated ? '50%' : '0%',
        }}
        transition={{ duration: 1 }}
      >
        {/* Stems and flowers in all directions - only show when activated */}
        {isActivated && (
          <>
            {/* Left stem and flower */}
            <motion.div
              className="absolute top-1/5 -left-1 w-8 h-1 bg-green-500 origin-left"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <motion.div
                className={`absolute -top-3 right-0 w-6 h-6 ${flowerColors[0]} rounded-full flex items-center justify-center`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.4, delay: 0.9 }}
              >
                <div className="absolute inset-1 bg-yellow-300 rounded-full" />
              </motion.div>
            </motion.div>

            {/* Right stem and flower */}
            <motion.div
              className="absolute top-1/4 -right-1 w-8 h-1 bg-green-500 origin-right"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <motion.div
                className={`absolute -top-3 left-0 w-6 h-6 ${flowerColors[1]} rounded-full flex items-center justify-center`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.4, delay: 1.1 }}
              >
                <div className="absolute inset-1 bg-yellow-300 rounded-full" />
              </motion.div>
            </motion.div>

            {/* Upper left stem and flower */}
            <motion.div
              className="absolute top-1/3 left-0 w-6 h-1 bg-green-500 origin-left rotate-[-30deg]"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <motion.div
                className={`absolute -top-3 right-0 w-5 h-5 ${flowerColors[2]} rounded-full flex items-center justify-center`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.4, delay: 1.3 }}
              >
                <div className="absolute inset-1 bg-yellow-300 rounded-full" />
              </motion.div>
            </motion.div>

            {/* Upper right stem and flower */}
            <motion.div
              className="absolute top-1/3 right-0 w-6 h-1 bg-green-500 origin-right rotate-[30deg]"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.9 }}
            >
              <motion.div
                className={`absolute -top-3 left-0 w-5 h-5 ${flowerColors[3]} rounded-full flex items-center justify-center`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.4, delay: 1.5 }}
              >
                <div className="absolute inset-1 bg-yellow-300 rounded-full" />
              </motion.div>
            </motion.div>

            {/* Top flower */}
            <motion.div
              className="absolute -top-7 left-1/2 -translate-x-1/2 w-7 h-7 bg-pink-500 rounded-full flex items-center justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 1 }}
            >
              <div className="absolute inset-1.5 bg-yellow-300 rounded-full" />
            </motion.div>
          </>
        )}
      </motion.div>

      {/* Soil/pot */}
      <motion.div
        className="absolute left-1/2 -translate-x-1/2 w-20 h-10 bg-amber-800 rounded-t-none rounded-b-lg"
        style={{ top: '90%' }}
        initial={{ opacity: 0.5, scale: 0.8 }}
        animate={{
          opacity: isActivated ? 1 : 0.5,
          scale: isActivated ? 1 : 0.8
        }}
        transition={{ duration: 0.5 }}
      >
        {/* Soil texture */}
        <div className="absolute inset-1 top-0 bg-amber-900/50 rounded-b-lg" />
      </motion.div>

      {/* Debug cursor position indicator - only visible during development */}
      {false && (
        <div
          className="absolute w-2 h-2 bg-red-500 rounded-full z-50"
          style={{
            left: mousePosition.x,
            top: mousePosition.y,
            transform: 'translate(-50%, -50%)'
          }}
        />
      )}
    </div>
  );
};
