'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export const LightbulbPlant = ({
  className,
}: {
  className?: string;
}) => {
  return (
    <div className={cn('relative w-full max-w-md mx-auto aspect-square', className)}>
      {/* Glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full bg-primary/20 blur-2xl"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          repeatType: 'reverse',
        }}
      />
      
      {/* Lightbulb */}
      <motion.div
        className="absolute top-1/4 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 0.5 }}
      >
        <div className="relative w-full h-full">
          {/* Bulb glass */}
          <div className="absolute inset-0 bg-gradient-to-b from-yellow-100/80 to-yellow-300/80 rounded-full backdrop-blur-sm border border-white/20" />
          
          {/* Filament */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-12">
            <motion.div 
              className="w-[2px] h-8 bg-yellow-400 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              initial={{ opacity: 0.2 }}
              animate={{ opacity: 1 }}
              transition={{ 
                duration: 0.5, 
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            />
            <motion.div 
              className="w-[2px] h-8 bg-yellow-400 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-90"
              initial={{ opacity: 0.2 }}
              animate={{ opacity: 1 }}
              transition={{ 
                duration: 0.7, 
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            />
          </div>
          
          {/* Base */}
          <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-10 h-4 bg-gray-300 rounded-md" />
        </div>
      </motion.div>
      
      {/* Plant stem */}
      <motion.div
        className="absolute top-1/4 left-1/2 -translate-x-1/2 w-1 bg-green-500"
        style={{ height: '50%', top: '40%' }}
        initial={{ height: '0%' }}
        animate={{ height: '50%' }}
        transition={{ duration: 2 }}
      >
        {/* Leaves */}
        <motion.div
          className="absolute top-1/4 -left-4 w-4 h-2 bg-green-400 rounded-full origin-right"
          initial={{ rotate: -45, scale: 0 }}
          animate={{ rotate: -45, scale: 1 }}
          transition={{ duration: 1, delay: 1 }}
        />
        <motion.div
          className="absolute top-2/4 -right-4 w-4 h-2 bg-green-400 rounded-full origin-left"
          initial={{ rotate: 45, scale: 0 }}
          animate={{ rotate: 45, scale: 1 }}
          transition={{ duration: 1, delay: 1.3 }}
        />
        <motion.div
          className="absolute top-3/4 -left-5 w-5 h-2 bg-green-400 rounded-full origin-right"
          initial={{ rotate: -45, scale: 0 }}
          animate={{ rotate: -45, scale: 1 }}
          transition={{ duration: 1, delay: 1.6 }}
        />
      </motion.div>
      
      {/* Hand holding the bulb */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 w-40 h-40"
        style={{ top: '60%' }}
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 1, delay: 0.2 }}
      >
        <div className="relative w-full h-full">
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-16 h-16 bg-[#e0c8b0] rounded-full" />
        </div>
      </motion.div>
    </div>
  );
};
