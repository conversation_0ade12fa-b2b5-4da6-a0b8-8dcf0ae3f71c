// Simple authentication utilities for the demo
// In a real application, this would integrate with a proper auth service

export interface User {
  id: string;
  name: string;
  email: string;
  isOnboarded: boolean;
  assessmentResults?: {
    analytical: number;
    creative: number;
    social: number;
    strategic: number;
  };
}

export const AUTH_STORAGE_KEY = 'innohub_auth';
export const USER_STORAGE_KEY = 'innohub_user';

export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    const authData = localStorage.getItem(AUTH_STORAGE_KEY);
    return authData === 'true';
  } catch {
    return false;
  }
}

export function getCurrentUser(): User | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const userData = localStorage.getItem(USER_STORAGE_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch {
    return null;
  }
}

export function login(email: string, password: string): Promise<User> {
  return new Promise((resolve, reject) => {
    // Simulate API call
    setTimeout(() => {
      if (email && password) {
        const user: User = {
          id: '1',
          name: email.split('@')[0],
          email,
          isOnboarded: localStorage.getItem('userOnboarded') === 'true',
        };

        // Check if user has assessment results
        const assessmentResults = localStorage.getItem('assessmentResults');
        if (assessmentResults) {
          try {
            user.assessmentResults = JSON.parse(assessmentResults);
          } catch {
            // Ignore parsing errors
          }
        }

        localStorage.setItem(AUTH_STORAGE_KEY, 'true');
        localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));

        // Set cookies for middleware
        document.cookie = `innohub_auth=true; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days
        if (user.isOnboarded) {
          document.cookie = `userOnboarded=true; path=/; max-age=${60 * 60 * 24 * 7}`;
        }

        resolve(user);
      } else {
        reject(new Error('Invalid credentials'));
      }
    }, 1000);
  });
}

export function signup(name: string, email: string, password: string): Promise<User> {
  return new Promise((resolve, reject) => {
    // Simulate API call
    setTimeout(() => {
      if (name && email && password) {
        const user: User = {
          id: Date.now().toString(),
          name,
          email,
          isOnboarded: false,
        };

        localStorage.setItem(AUTH_STORAGE_KEY, 'true');
        localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));

        // Set auth cookie for middleware
        document.cookie = `innohub_auth=true; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days

        resolve(user);
      } else {
        reject(new Error('Invalid data'));
      }
    }, 1000);
  });
}

export function logout(): void {
  localStorage.removeItem(AUTH_STORAGE_KEY);
  localStorage.removeItem(USER_STORAGE_KEY);

  // Clear cookies
  document.cookie = 'innohub_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
  document.cookie = 'userOnboarded=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
}

export function updateUser(updates: Partial<User>): void {
  const currentUser = getCurrentUser();
  if (currentUser) {
    const updatedUser = { ...currentUser, ...updates };
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUser));

    // If updating onboarding status, also update the cookie
    if (updates.isOnboarded !== undefined) {
      if (updates.isOnboarded) {
        document.cookie = `userOnboarded=true; path=/; max-age=${60 * 60 * 24 * 7}`;
      } else {
        document.cookie = 'userOnboarded=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
      }
    }
  }
}

export function markUserAsOnboarded(): void {
  updateUser({ isOnboarded: true });
  localStorage.setItem('userOnboarded', 'true');
}
