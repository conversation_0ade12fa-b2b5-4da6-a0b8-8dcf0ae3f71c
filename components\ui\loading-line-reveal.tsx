'use client';

import { motion } from 'framer-motion';

interface LoadingLineRevealProps {
  progress: number;
  height?: number;
  className?: string;
}

export function LoadingLineReveal({ 
  progress, 
  height = 2,
  className = ''
}: LoadingLineRevealProps) {
  return (
    <div className={`w-full bg-gray-800 rounded-full overflow-hidden ${className}`} style={{ height }}>
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ 
          duration: 0.2,
          ease: "easeOut"
        }}
        className="h-full relative"
      >
        {/* Main progress line */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-purple-400 to-purple-600" />
        
        {/* Glow effect */}
        <div className="absolute inset-0 bg-purple-500 blur-sm" />
        
        {/* Animated pulse at the end of the line */}
        {progress < 100 && (
          <motion.div 
            className="absolute top-1/2 right-0 w-4 h-4 -translate-y-1/2 translate-x-1/2 rounded-full bg-purple-400"
            animate={{
              opacity: [0.7, 1, 0.7],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />
        )}
      </motion.div>
    </div>
  );
}
