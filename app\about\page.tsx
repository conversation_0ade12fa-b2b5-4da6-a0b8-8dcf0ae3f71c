'use client';

import { useState, useEffect, useRef } from 'react';
import { CursorImageEffect } from '@/components/ui/aceternity/cursor-image-effect';
import { useLanguage } from '@/lib/context/language-context';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';

export default function AboutPage() {
  const { t, language } = useLanguage();
  const [activeSection, setActiveSection] = useState('whatWeDo');

  // Refs for each section
  const whatWeDoRef = useRef<HTMLDivElement>(null);
  const ourTeamRef = useRef<HTMLDivElement>(null);
  const mentorsRef = useRef<HTMLDivElement>(null);

  // Investor images for cursor effect
  const investorImages = [
    '/images/investors/1.png',
    '/images/investors/2.png',
    '/images/investors/3.png',
    '/images/investors/4.png',
    '/images/investors/5.png',
  ];

  // Team members data
  const teamMembers = [
    {
      name: t('about.teamMember1'),
      role: t('about.teamMember1Role'),
      image: '/images/investors/1.png', // Using placeholder image
    },
    {
      name: t('about.teamMember2'),
      role: t('about.teamMember2Role'),
      image: '/images/investors/2.png', // Using placeholder image
    },
  ];

  // Mentors data
  const mentors = [
    {
      name: t('about.mentor1'),
      role: t('about.mentor1Role'),
      image: '/images/investors/3.png', // Using placeholder image
    },
    {
      name: t('about.mentor2'),
      role: t('about.mentor2Role'),
      image: '/images/investors/4.png', // Using placeholder image
    },
  ];

  // What we do paragraphs
  const whatWeDoItems = [
    t('about.whatWeDoItem1'),
    t('about.whatWeDoItem2'),
    t('about.whatWeDoItem3'),
    t('about.whatWeDoItem4'),
    t('about.whatWeDoItem5'),
  ];

  // Navigation items
  const navItems = [
    { id: 'whatWeDo', label: t('about.whatWeDo'), ref: whatWeDoRef },
    { id: 'ourTeam', label: t('about.ourTeam'), ref: ourTeamRef },
    { id: 'mentors', label: t('about.mentors'), ref: mentorsRef },
  ];

  // Scroll to section when clicking on nav item
  const scrollToSection = (id: string) => {
    const sectionRef = navItems.find(item => item.id === id)?.ref;
    if (sectionRef?.current) {
      sectionRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Update active section based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 200; // Adding offset for better UX

      // Check which section is in view
      if (whatWeDoRef.current && scrollPosition >= whatWeDoRef.current.offsetTop &&
          ourTeamRef.current && scrollPosition < ourTeamRef.current.offsetTop) {
        setActiveSection('whatWeDo');
      } else if (ourTeamRef.current && scrollPosition >= ourTeamRef.current.offsetTop &&
                mentorsRef.current && scrollPosition < mentorsRef.current.offsetTop) {
        setActiveSection('ourTeam');
      } else if (mentorsRef.current && scrollPosition >= mentorsRef.current.offsetTop) {
        setActiveSection('mentors');
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-auto">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* First page with cursor effect */}
      <CursorImageEffect
        className="min-h-[90vh] flex flex-col items-center justify-center pt-32 pb-20 bg-black relative"
        images={investorImages}
        imageSize={300}
        imageOpacity={0.8} // Slightly transparent
      >
        {/* Empty container - only cursor effect */}
        {/* You can add content here later */}
      </CursorImageEffect>

      {/* Content sections */}
      <section className="bg-black py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Left sidebar navigation */}
            <div className="md:w-1/4">
              <div className="sticky top-32 space-y-4">
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={cn(
                      "text-left w-full py-2 px-4 rounded-lg transition-all",
                      activeSection === item.id
                        ? "bg-primary/20 text-white font-medium border-l-4 border-primary"
                        : "text-white/70 hover:text-white hover:bg-white/5"
                    )}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Right content area */}
            <div className="md:w-3/4 space-y-32"> {/* Added space between sections */}
              {/* What We Do Section */}
              <motion.div
                ref={whatWeDoRef}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-8 pt-4"
                id="whatWeDo"
              >
                <div className="mb-12">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true }}
                    className="inline-block rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-4"
                  >
                    Our Mission
                  </motion.div>
                  <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
                    {t('about.whatWeDoTitle')}
                  </h2>
                  <p className="text-white/70 text-lg leading-relaxed max-w-3xl">
                    We empower entrepreneurs and innovators through comprehensive support, cutting-edge resources, and strategic guidance.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {whatWeDoItems.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{ y: -5 }}
                      className="group relative"
                    >
                      <div className="relative bg-black/40 backdrop-blur-md border border-primary/10 rounded-xl p-6 h-full transition-all duration-300 hover:border-primary/30 hover:bg-black/50">
                        {/* Icon or number indicator */}
                        <div className="flex items-center mb-4">
                          <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold text-sm mr-3">
                            {(index + 1).toString().padStart(2, '0')}
                          </div>
                          <div className="h-px flex-1 bg-gradient-to-r from-primary/30 to-transparent"></div>
                        </div>

                        <p className="text-white/90 leading-relaxed">{item}</p>

                        {/* Hover glow effect */}
                        <motion.div
                          className="absolute inset-0 pointer-events-none rounded-xl"
                          initial={{ opacity: 0 }}
                          whileHover={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                          style={{
                            background: 'radial-gradient(circle at 50% 0%, rgba(var(--primary-rgb), 0.1), transparent 70%)',
                          }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Our Team Section */}
              <motion.div
                ref={ourTeamRef}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="space-y-8 pt-4"
                id="ourTeam"
              >
                <h2 className="text-3xl font-bold text-white">
                  {t('about.ourTeamTitle')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {teamMembers.map((member, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="bg-black/30 backdrop-blur-md border border-primary/10 rounded-xl p-6 flex flex-col items-center"
                    >
                      <div className="relative w-32 h-32 mb-4">
                        <Image
                          src={member.image}
                          alt={member.name}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                      <h3 className="text-xl font-semibold text-white">{member.name}</h3>
                      <p className="text-white/70 text-center mt-2">{member.role}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Mentors Section */}
              <motion.div
                ref={mentorsRef}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="space-y-8 pt-4"
                id="mentors"
              >
                <div className="mb-12">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true }}
                    className="inline-block rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-4"
                  >
                    Expert Guidance
                  </motion.div>
                  <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
                    {t('about.mentorsTitle')}
                  </h2>
                  <p className="text-white/70 text-lg leading-relaxed max-w-3xl">
                    Learn from industry veterans and successful entrepreneurs who provide personalized guidance and strategic insights.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {mentors.map((mentor, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{ y: -5 }}
                      className="group relative"
                    >
                      <div className="relative bg-black/40 backdrop-blur-md border border-primary/10 rounded-xl p-8 h-full transition-all duration-300 hover:border-primary/30 hover:bg-black/50">
                        {/* Profile section */}
                        <div className="flex items-center space-x-6 mb-6">
                          <div className="relative">
                            <div className="relative w-20 h-20 rounded-full overflow-hidden">
                              <Image
                                src={mentor.image}
                                alt={mentor.name}
                                fill
                                className="object-cover filter grayscale group-hover:grayscale-0 transition-all duration-500"
                              />
                            </div>
                            {/* Animated ring */}
                            <motion.div
                              className="absolute inset-0 rounded-full border-2 border-primary/30"
                              initial={{ scale: 1, opacity: 0 }}
                              whileHover={{ scale: 1.1, opacity: 1 }}
                              transition={{ duration: 0.3 }}
                            />
                          </div>

                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-white mb-1 group-hover:text-primary transition-colors duration-300">
                              {mentor.name}
                            </h3>
                            <p className="text-primary/80 text-sm font-medium">{mentor.role}</p>
                          </div>
                        </div>

                        {/* Expertise indicators */}
                        <div className="flex flex-wrap gap-2">
                          {['Strategy', 'Growth', 'Funding'].map((skill, skillIndex) => (
                            <motion.span
                              key={skill}
                              initial={{ opacity: 0, scale: 0.8 }}
                              whileInView={{ opacity: 1, scale: 1 }}
                              transition={{ delay: index * 0.1 + skillIndex * 0.05 }}
                              className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium transition-colors border-primary/30 bg-primary/10 text-primary hover:bg-primary/20"
                            >
                              {skill}
                            </motion.span>
                          ))}
                        </div>

                        {/* Hover glow effect */}
                        <motion.div
                          className="absolute inset-0 pointer-events-none rounded-xl"
                          initial={{ opacity: 0 }}
                          whileHover={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                          style={{
                            background: 'radial-gradient(circle at 50% 0%, rgba(var(--primary-rgb), 0.1), transparent 70%)',
                          }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
