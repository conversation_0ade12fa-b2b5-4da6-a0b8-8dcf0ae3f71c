'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface BokehProps {
  children: React.ReactNode;
  className?: string;
  density?: number;
  speed?: number;
  colors?: string[];
}

export const BokehBackground = ({
  children,
  className,
  density = 50, // Increased default density
  speed = 2, // Slower for smoother movement
  colors = ['#ffcc33', '#ffaa00', '#ff8800', '#ffffff'],
}: BokehProps) => {
  const [bokehElements, setBokehElements] = useState<React.ReactNode[]>([]);

  useEffect(() => {
    const elements = [];

    // Create multiple layers of bokeh elements for depth
    for (let layer = 0; layer < 3; layer++) {
      const layerDensity = Math.floor(density / 3);

      // Determine blur class based on layer
      let layerBlur: string;
      if (layer === 0) {
        layerBlur = 'blur-sm';
      } else if (layer === 1) {
        layerBlur = 'blur-md';
      } else {
        layerBlur = 'blur-lg';
      }

      // Determine opacity multiplier based on layer
      let layerOpacityMultiplier: number;
      if (layer === 0) {
        layerOpacityMultiplier = 1;
      } else if (layer === 1) {
        layerOpacityMultiplier = 0.7;
      } else {
        layerOpacityMultiplier = 0.4;
      }

      for (let i = 0; i < layerDensity; i++) {
        const size = Math.random() * 120 + 30; // 30-150px for more variety
        const x = Math.random() * 110 - 5; // Allow slight overflow for edge effects
        const y = Math.random() * 110 - 5;
        const delay = Math.random() * 8; // Longer delay range for staggered start
        const duration = (Math.random() * 15 + 20) / speed; // Longer duration for smoother movement
        const color = colors[Math.floor(Math.random() * colors.length)];
        const baseOpacity = (Math.random() * 0.4 + 0.1) * layerOpacityMultiplier; // 0.1-0.5 adjusted by layer

        // Create more complex movement patterns
        const movementRange = 60 + Math.random() * 40; // 60-100px movement range
        const rotationSpeed = Math.random() * 360;

        elements.push(
          <motion.div
            key={`${layer}-${i}`}
            className={`absolute rounded-full ${layerBlur}`}
            style={{
              width: `${size}px`,
              height: `${size}px`,
              left: `${x}%`,
              top: `${y}%`,
              backgroundColor: color,
              opacity: baseOpacity,
              filter: `blur(${2 + layer * 3}px)`, // Additional blur for depth
            }}
            animate={{
              x: [
                -movementRange/2,
                movementRange/2,
                -movementRange/4,
                movementRange/3,
                -movementRange/2,
              ],
              y: [
                -movementRange/3,
                movementRange/4,
                movementRange/2,
                -movementRange/2,
                -movementRange/3,
              ],
              opacity: [
                baseOpacity,
                baseOpacity * 1.8,
                baseOpacity * 0.6,
                baseOpacity * 1.4,
                baseOpacity,
              ],
              scale: [1, 1.2, 0.8, 1.1, 1],
              rotate: [0, rotationSpeed, rotationSpeed * 2],
            }}
            transition={{
              duration,
              repeat: Infinity,
              repeatType: 'loop',
              delay,
              ease: [0.25, 0.46, 0.45, 0.94], // Custom cubic-bezier for smoother easing
            }}
          />
        );
      }
    }

    // Add some larger, more prominent floating elements
    for (let i = 0; i < Math.floor(density / 10); i++) {
      const size = Math.random() * 200 + 100; // 100-300px large elements
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const delay = Math.random() * 10;
      const duration = (Math.random() * 25 + 30) / speed; // Very slow movement
      const color = colors[Math.floor(Math.random() * colors.length)];
      const opacity = Math.random() * 0.15 + 0.05; // Very subtle

      elements.push(
        <motion.div
          key={`large-${i}`}
          className="absolute rounded-full blur-3xl"
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}%`,
            top: `${y}%`,
            backgroundColor: color,
            opacity,
          }}
          animate={{
            x: [-30, 30, -20, 25, -30],
            y: [-20, 25, 30, -25, -20],
            opacity: [opacity, opacity * 2, opacity * 0.5, opacity * 1.5, opacity],
            scale: [1, 1.3, 0.7, 1.2, 1],
          }}
          transition={{
            duration,
            repeat: Infinity,
            repeatType: 'loop',
            delay,
            ease: [0.23, 1, 0.32, 1], // Smooth easing
          }}
        />
      );
    }

    setBokehElements(elements);
  }, [density, speed, colors]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div className="absolute inset-0 z-0">
        {bokehElements}
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-10" />
      </div>
      <div className="relative z-20">{children}</div>
    </div>
  );
};

export const GlassCard = ({
  children,
  className,
  hoverEffect = true,
}: {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: boolean;
}) => {
  return (
    <motion.div
      className={cn(
        'relative backdrop-blur-md bg-white/5 border border-white/10 rounded-xl overflow-hidden',
        hoverEffect && 'transition-all duration-300 hover:bg-white/10 hover:shadow-lg',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent opacity-30" />
      <div className="relative z-10">{children}</div>
    </motion.div>
  );
};
