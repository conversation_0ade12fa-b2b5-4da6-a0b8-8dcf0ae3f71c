'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

export const Spotlight = ({
  children,
  className = '',
  size = 600,
}: {
  children: React.ReactNode;
  className?: string;
  size?: number;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    
    const { left, top } = containerRef.current.getBoundingClientRect();
    const x = e.clientX - left;
    const y = e.clientY - top;
    
    setPosition({ x, y });
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    setOpacity(0);
  };

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      <motion.div
        className="pointer-events-none absolute -inset-px z-10 opacity-0"
        animate={{
          opacity,
          transition: { duration: 0.3 },
        }}
        style={{
          background: `radial-gradient(${size}px circle at ${position.x}px ${position.y}px, hsl(var(--primary)/0.15), transparent 80%)`,
        }}
      />
      {children}
    </div>
  );
};
