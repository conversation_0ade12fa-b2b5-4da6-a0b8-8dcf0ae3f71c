'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/context/language-context';
import { Spotlight } from '@/components/ui/spotlight-new';

export default function HeroSection() {
  const { t } = useLanguage();

  return (
    <div className="relative min-h-screen flex flex-col items-center justify-center pt-20 pb-10 overflow-hidden font-sans shadow-b-lg">
      {/* Background with dark gradient and stars */}
      {/* Background Image */}
      {/* Background Image with Animation */}
      <motion.div
        className="absolute inset-0 z-0 bg-cover bg-center"
        style={{ backgroundImage: "url('/images/Background/hero-back.jpg')" }}
        initial={{ scale: 1 }}
        animate={{ scale: 1.05 }}
        transition={{ duration: 10, repeat: Infinity, repeatType: 'reverse', ease: "easeInOut" }}
      >
        {/* Overlay for better text readability and smoother transition */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-black/98 to-black/90 opacity-30"></div>
      </motion.div>

      {/* Subtle Animated stars/particles */}
      {/* Subtle Animated stars/particles */}
      <div className="absolute inset-0 z-0 opacity-30">
        {Array.from({ length: 80 }).map((_, i) => (
          <motion.div
            key={`star-${i}-${Math.random().toString(36).substring(2, 9)}`}
            className="absolute rounded-full bg-white"
            style={{
              width: Math.random() * 1.5 + 0.5 + 'px',
              height: Math.random() * 1.5 + 0.5 + 'px',
              left: Math.random() * 100 + '%',
              top: Math.random() * 100 + '%',
            }}
            animate={{
              opacity: [0, 0.6, 0],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: Math.random() * 4 + 3,
              repeat: Infinity,
              delay: Math.random() * 6,
            }}
          />
        ))}
      </div>

      {/* Spotlight Effect */}
      <Spotlight />

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full h-full flex items-center justify-between">
        {/* Left Content */}
        <div className="flex flex-col items-start text-left max-w-md">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight text-white font-sans"
            style={{ fontFamily: "'Montserrat', sans-serif" }}
          >
            {t('hero.title')}
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="text-lg text-gray-300 mt-4"
          >
            {t('hero.subtitle')}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="mt-8"
          >
            <Button
              size="lg"
              asChild
              className="rounded-full border border-purple-500 text-white bg-transparent hover:bg-purple-700/20 group px-6 py-2 h-auto"
            >
              <Link href="/ai-solutions" className="flex items-center">
                read more ...
              </Link>
            </Button>
          </motion.div>
        </div>

        {/* Right Content */}
        <div className="flex flex-col items-end text-right max-w-md">
           <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight text-white font-sans"
            style={{ fontFamily: "'Montserrat', sans-serif" }}
          >
            {t('hero.secondaryTitle')}
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="text-lg text-gray-300 mt-4"
          >
            {t('hero.secondarySubtitle')}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="mt-8"
          >
            <Button
              size="lg"
              asChild
              className="rounded-full border border-purple-500 text-white bg-transparent hover:bg-purple-700/20 group px-6 py-2 h-auto"
            >
              <Link href="/contact" className="flex items-center">
                read more ...
              </Link>
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
