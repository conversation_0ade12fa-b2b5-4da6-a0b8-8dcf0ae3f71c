# Debug Flow for Courses Navigation

## Expected User Journey

1. **Start**: User clicks "Courses" button in navbar → `/courses`
2. **Authentication Check**: If not authenticated → redirect to `/auth/login`
3. **Login/Signup**: User completes authentication → redirect to `/onboarding`
4. **Onboarding**: User completes assessment → redirect to `/onboarding/results`
5. **Results**: User clicks "хичээлүүдээ судлах" → redirect to `/courses`
6. **Courses Dashboard**: User sees personalized course dashboard

## Debugging Steps

### Step 1: Test Unauthenticated Access
1. Open browser in incognito mode
2. Go to `http://localhost:3001`
3. Click "Courses" button
4. **Expected**: Should redirect to `/courses/welcome`

### Step 2: Test Authentication Flow
1. From welcome page, click "Get Started" or "Sign In"
2. **Expected**: Should go to `/auth/login`
3. Fill in login form (any email/password)
4. **Expected**: Should redirect to `/onboarding`

### Step 3: Test Onboarding Flow
1. Complete the 5 assessment questions
2. Click "Submit Assessment"
3. **Expected**: Should redirect to `/onboarding/results`

### Step 4: Test Results to Courses Navigation
1. On results page, click "хичээлүүдээ судлах" button
2. **Expected**: Should redirect to `/courses` with personalized dashboard

## Key Files Modified

1. **`app/onboarding/results/page.tsx`**:
   - Fixed `handleContinueToCourses()` function
   - Now uses `markUserAsOnboarded()` from auth system
   - Forces page reload to ensure middleware picks up cookies

2. **`lib/auth.ts`**:
   - Added `markUserAsOnboarded()` function
   - Properly sets both localStorage and cookies
   - Updates user object with onboarding status

3. **`components/auth/ProtectedRoute.tsx`**:
   - Enhanced onboarding check logic
   - Checks both user object and localStorage flag

4. **`middleware.ts`**:
   - Simplified and cleaned up routing logic
   - Proper cookie checking for authentication and onboarding

## Common Issues and Solutions

### Issue 1: Stuck in redirect loop
**Cause**: Cookies not being set properly
**Solution**: Check browser dev tools → Application → Cookies
**Expected cookies**: `innohub_auth=true`, `userOnboarded=true`

### Issue 2: Redirected back to onboarding after completing it
**Cause**: `userOnboarded` cookie not set
**Solution**: Check `markUserAsOnboarded()` function execution

### Issue 3: Not redirected to courses after results
**Cause**: Navigation function not working
**Solution**: Using `window.location.href` for forced navigation

## Testing Checklist

- [ ] Unauthenticated user sees welcome page
- [ ] Login redirects to onboarding for new users
- [ ] Onboarding assessment works correctly
- [ ] Results page shows talent analysis
- [ ] "Continue to Courses" button works
- [ ] Courses dashboard shows personalized content
- [ ] User can navigate back and forth without issues

## Browser Console Commands for Debugging

```javascript
// Check authentication status
localStorage.getItem('innohub_auth')

// Check onboarding status
localStorage.getItem('userOnboarded')

// Check user object
JSON.parse(localStorage.getItem('innohub_user') || '{}')

// Check cookies
document.cookie

// Clear all data (for testing)
localStorage.clear()
document.cookie = 'innohub_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
document.cookie = 'userOnboarded=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
```
