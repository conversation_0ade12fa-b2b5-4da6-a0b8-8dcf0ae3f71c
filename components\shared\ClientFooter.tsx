'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Import the Footer component with SSR disabled
const Footer = dynamic(() => import('./Footer'), { ssr: false });

export default function ClientFooter() {
  return (
    <Suspense fallback={<FooterFallback />}>
      <Footer />
    </Suspense>
  );
}

function FooterFallback() {
  return (
    <footer className="border-t border-primary/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and tagline placeholder */}
          <div>
            <div className="flex items-center gap-2">
              <div className="relative w-10 h-10 bg-gradient-to-br from-primary to-primary/70 rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-xl">I</span>
              </div>
              <span className="font-bold text-xl">INNO<span className="text-primary">HUB</span></span>
            </div>
            <div className="mt-4 w-full h-4 bg-gray-800 rounded"></div>
            <div className="mt-2 w-3/4 h-4 bg-gray-800 rounded"></div>
            <div className="mt-6 flex space-x-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="w-8 h-8 rounded-full bg-gray-800"></div>
              ))}
            </div>
          </div>
          
          {/* Footer links placeholders */}
          {[1, 2, 3].map((section) => (
            <div key={section}>
              <div className="h-6 w-24 bg-gray-800 rounded mb-4"></div>
              <div className="space-y-2">
                {[1, 2, 3, 4].map((link) => (
                  <div key={link} className="h-4 w-32 bg-gray-800 rounded"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        {/* Copyright and links placeholder */}
        <div className="mt-16 pt-8 border-t border-primary/20 flex flex-col md:flex-row justify-between items-center">
          <div className="h-4 w-48 bg-gray-800 rounded"></div>
          <div className="mt-4 md:mt-0 flex gap-4">
            <div className="h-4 w-24 bg-gray-800 rounded"></div>
            <div className="h-4 w-24 bg-gray-800 rounded"></div>
          </div>
        </div>
      </div>
    </footer>
  );
}
