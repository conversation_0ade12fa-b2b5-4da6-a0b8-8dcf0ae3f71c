'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { DollarSign, TrendingUp, Users, Target } from 'lucide-react';

interface FinancialInfoProps {
  company: Company;
}

export function FinancialInfo({ company }: FinancialInfoProps) {
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-400" />
              </div>
              <AuthCardTitle>Financial Overview</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-400 mx-auto mb-2" />
                <p className="text-lg font-bold text-green-400">$500K</p>
                <p className="text-xs text-muted-foreground">Seed Funding Target</p>
              </div>
              <div className="text-center p-4 bg-blue-500/5 border border-blue-500/20 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-400 mx-auto mb-2" />
                <p className="text-lg font-bold text-blue-400">$50K</p>
                <p className="text-xs text-muted-foreground">Monthly Revenue Goal</p>
              </div>
              <div className="text-center p-4 bg-purple-500/5 border border-purple-500/20 rounded-lg">
                <Users className="h-6 w-6 text-purple-400 mx-auto mb-2" />
                <p className="text-lg font-bold text-purple-400">100</p>
                <p className="text-xs text-muted-foreground">Target Customers Y1</p>
              </div>
              <div className="text-center p-4 bg-orange-500/5 border border-orange-500/20 rounded-lg">
                <Target className="h-6 w-6 text-orange-400 mx-auto mb-2" />
                <p className="text-lg font-bold text-orange-400">$2M</p>
                <p className="text-xs text-muted-foreground">Series A Target</p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Revenue Model</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Subscription-based SaaS model with tiered pricing based on farm size and feature requirements.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { tier: 'Basic', price: '$99/month', features: 'Up to 50 hectares, Basic monitoring' },
                  { tier: 'Professional', price: '$299/month', features: 'Up to 200 hectares, Advanced analytics' },
                  { tier: 'Enterprise', price: '$599/month', features: 'Unlimited hectares, Custom solutions' }
                ].map((plan, index) => (
                  <div key={index} className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
                    <h4 className="font-medium text-white mb-1">{plan.tier}</h4>
                    <p className="text-lg font-bold text-primary mb-2">{plan.price}</p>
                    <p className="text-sm text-muted-foreground">{plan.features}</p>
                  </div>
                ))}
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
