/* Timeline 3D Effects */
.timeline-section {
  position: relative;
  overflow: visible;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  background-color: #000;
}

.timeline-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 8rem 0;
  position: relative;
}

.timeline-image-side {
  width: 55%;
  position: relative;
}

.timeline-content-side {
  width: 40%;
  padding-left: 3rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.timeline-image-container {
  position: relative;
  height: 500px;
  perspective: 2000px;
  z-index: 10;
  width: 100%;
}

.timeline-image-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  will-change: transform;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.45);
  background-color: #111;
}

/* Image blur effect container */
.timeline-image-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(10px);
  opacity: 0.5;
  border-radius: 24px;
  z-index: 1;
  padding: 2rem;
}

/* Main image container */
.timeline-image-main {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 24px;
  opacity: 0.9;
  will-change: transform;
  z-index: 2;
  padding: 2rem;
}

/* Title styling */
.timeline-title {
  font-size: 3.5rem;
  font-weight: bold;
  color: #a855f7; /* Purple color */
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

/* Caption styling */
.timeline-image-caption {
  font-style: italic;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  margin-top: 1rem;
  margin-bottom: 2rem;
}

/* Description styling */
.timeline-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  line-height: 1.6;
  max-width: 400px;
}

/* Button styling */
.timeline-content-side button {
  background-color: #1a1a1a;
  color: white;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.timeline-content-side button:hover {
  background-color: #333;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .timeline-item {
    flex-direction: column;
    margin: 6rem 0;
  }

  .timeline-image-side,
  .timeline-content-side {
    width: 100%;
  }

  .timeline-content-side {
    padding-left: 0;
    margin-top: 2rem;
    align-items: center;
    text-align: center;
  }

  .timeline-content-side .flex {
    justify-content: center;
  }

  .timeline-image-container {
    height: 400px;
  }

  .timeline-title {
    font-size: 2.5rem;
  }

  .timeline-description {
    max-width: 100%;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .timeline-image-container {
    height: 300px;
  }

  .timeline-title {
    font-size: 2rem;
  }

  .timeline-section {
    padding: 0 1rem;
  }
}
