'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { companies, Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, Auth<PERSON>ard<PERSON>eader, AuthCardTitle } from '@/components/ui/auth-card';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { CompanyWelcome } from '@/components/companies/CompanyWelcome';
import { StepGuide } from '@/components/companies/StepGuide';
import { MentorCards } from '@/components/companies/MentorCards';
import { MilestoneTimeline } from '@/components/companies/MilestoneTimeline';
import { ResourcesSection } from '@/components/companies/ResourcesSection';
import { FAQSection } from '@/components/companies/FAQSection';
import { CompanyProfile } from '@/components/companies/CompanyProfile';
import { NavigationTabs } from '@/components/companies/NavigationTabs';
import { Breadcrumb, breadcrumbConfigs } from '@/components/ui/breadcrumb';
import { ArrowLeft, Building2 } from 'lucide-react';
import Link from 'next/link';

export default function CompanyInstructionsPage() {
  const params = useParams();
  const companyId = params.companyId as string;
  const [company, setCompany] = useState<Company | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch company data
    const fetchCompany = () => {
      setIsLoading(true);
      setTimeout(() => {
        const foundCompany = companies.find(c => c.id === companyId);
        setCompany(foundCompany || null);
        setIsLoading(false);
      }, 500);
    };

    if (companyId) {
      fetchCompany();
    }
  }, [companyId]);

  if (isLoading) {
    return (
      <AnimatedGradientBackground className="min-h-screen flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <p className="text-white text-lg">Loading company information...</p>
        </motion.div>
      </AnimatedGradientBackground>
    );
  }

  if (!company) {
    return (
      <AnimatedGradientBackground className="min-h-screen flex items-center justify-center">
        <AuthCard className="max-w-md">
          <AuthCardHeader>
            <div className="flex justify-center mb-4">
              <div className="bg-red-500/10 p-4 rounded-full">
                <Building2 className="h-8 w-8 text-red-500" />
              </div>
            </div>
            <AuthCardTitle>Company Not Found</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="text-center space-y-4">
              <p className="text-muted-foreground">
                The company with ID "{companyId}" was not found in our system.
              </p>
              <Link
                href="/programs"
                className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Programs
              </Link>
            </div>
          </AuthCardContent>
        </AuthCard>
      </AnimatedGradientBackground>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📋' },
    { id: 'profile', label: 'Company Profile', icon: '🏢' },
    { id: 'mentors', label: 'Mentors', icon: '👥' },
    { id: 'milestones', label: 'Milestones', icon: '🎯' },
    { id: 'resources', label: 'Resources', icon: '📚' },
    { id: 'faq', label: 'FAQ', icon: '❓' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <StepGuide company={company} />;
      case 'profile':
        return <CompanyProfile company={company} />;
      case 'mentors':
        return <MentorCards mentors={company.mentors} />;
      case 'milestones':
        return <MilestoneTimeline milestones={company.milestones} />;
      case 'resources':
        return <ResourcesSection resources={company.resources} />;
      case 'faq':
        return <FAQSection faq={company.faq} />;
      default:
        return <StepGuide company={company} />;
    }
  };

  return (
    <AnimatedGradientBackground className="min-h-screen">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header with Breadcrumb */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Breadcrumb
            items={breadcrumbConfigs.companyInstructions(company.name)}
            className="mb-6"
          />
        </motion.div>

        {/* Company Welcome Section */}
        <CompanyWelcome company={company} />

        {/* Navigation Tabs */}
        <NavigationTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-8"
        >
          {renderTabContent()}
        </motion.div>
      </div>
    </AnimatedGradientBackground>
  );
}
