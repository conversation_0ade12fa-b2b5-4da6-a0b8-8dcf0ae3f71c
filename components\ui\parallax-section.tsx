'use client';

import React, { ReactNode, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ParallaxSectionProps {
  children: ReactNode;
  className?: string;
  direction?: 'up' | 'down';
  speed?: number;
  offset?: number;
}

export function ParallaxSection({
  children,
  className,
  direction = 'up',
  speed = 0.2,
  offset = 0,
}: ParallaxSectionProps) {
  const ref = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start']
  });
  
  // Calculate parallax effect based on direction and speed
  const factor = direction === 'up' ? -speed : speed;
  const y = useTransform(scrollYProgress, [0, 1], [offset, offset + 100 * factor]);
  
  return (
    <motion.div
      ref={ref}
      className={cn('relative overflow-hidden', className)}
    >
      <motion.div style={{ y }} className="w-full h-full">
        {children}
      </motion.div>
    </motion.div>
  );
}

interface ParallaxImageProps {
  src: string;
  alt: string;
  className?: string;
  speed?: number;
}

export function ParallaxImage({ src, alt, className, speed = 0.3 }: ParallaxImageProps) {
  const ref = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start']
  });
  
  const y = useTransform(scrollYProgress, [0, 1], ['0%', `${speed * 100}%`]);
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1 + speed * 0.2]);
  
  return (
    <div ref={ref} className={cn('relative overflow-hidden', className)}>
      <motion.img
        src={src}
        alt={alt}
        style={{ y, scale }}
        className="w-full h-full object-cover"
      />
    </div>
  );
}
