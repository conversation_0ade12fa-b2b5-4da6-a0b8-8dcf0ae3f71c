'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InvestorCompanyCardProps {
  name: string;
  logo: string;
  description: string;
  investmentFocus: string[];
  website: string;
  featured?: boolean;
  index?: number;
}

export function InvestorCompanyCard({
  name,
  logo,
  description,
  investmentFocus,
  website,
  featured = false,
  index = 0,
}: InvestorCompanyCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // Mouse position values for 3D effect
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Smooth spring physics for mouse movement
  const springConfig = { damping: 25, stiffness: 300 };
  const smoothMouseX = useSpring(mouseX, springConfig);
  const smoothMouseY = useSpring(mouseY, springConfig);

  // Transform mouse position into rotation values
  const rotateX = useTransform(smoothMouseY, [-100, 100], [5, -5]);
  const rotateY = useTransform(smoothMouseX, [-100, 100], [-5, 5]);

  // Glow position based on mouse
  const glowX = useTransform(smoothMouseX, [-100, 100], [0, 100]);
  const glowY = useTransform(smoothMouseY, [-100, 100], [0, 100]);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    mouseX.set(e.clientX - centerX);
    mouseY.set(e.clientY - centerY);
  };

  return (
    <motion.div
      ref={cardRef}
      className={cn(
        'relative overflow-hidden rounded-xl bg-black/40 backdrop-blur-sm border border-white/10',
        'transform-gpu shadow-lg shadow-black/30 will-change-transform h-full',
        featured ? 'md:col-span-2' : ''
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.7,
        delay: index * 0.1,
        ease: [0.19, 1, 0.22, 1]
      }}
      viewport={{ once: true, margin: "-50px" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        mouseX.set(0);
        mouseY.set(0);
      }}
      onMouseMove={handleMouseMove}
      style={{
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
        perspective: 1000,
      }}
      whileHover={{
        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.5)",
        borderColor: "rgba(var(--primary-rgb), 0.3)",
      }}
    >
      {/* Colorful glow effect that follows cursor */}
      <motion.div
        className="absolute inset-0 z-0 opacity-0"
        initial={{ opacity: 0 }}
        animate={{
          opacity: isHovered ? 0.3 : 0,
        }}
        transition={{ duration: 0.3 }}
        style={{
          background: `radial-gradient(circle at ${glowX}% ${glowY}%, hsl(var(--primary)) 0%, transparent 70%)`,
          filter: 'blur(20px)',
        }}
      />

      <div className="p-6 flex flex-col h-full relative z-10">
        {/* Logo */}
        <div className="relative w-24 h-24 mb-4 overflow-hidden rounded-lg">
          <motion.div
            initial={{ scale: 0.9, opacity: 0.8 }}
            whileHover={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Image
              src={logo}
              alt={name}
              fill
              className="object-contain"
              sizes="(max-width: 768px) 96px, 96px"
            />
          </motion.div>
        </div>

        {/* Content */}
        <div className="flex-grow">
          <h3 className="text-xl font-bold text-white mb-2">{name}</h3>
          <p className="text-white/70 mb-4 text-sm">{description}</p>
          
          <div className="mb-4">
            <h4 className="text-xs uppercase text-white/50 mb-2">Investment Focus</h4>
            <div className="flex flex-wrap gap-2">
              {investmentFocus.map((focus, i) => (
                <span 
                  key={i}
                  className="text-xs px-2 py-1 rounded-full bg-primary/20 text-primary"
                >
                  {focus}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Footer with website link */}
        <motion.div 
          className="mt-4 pt-4 border-t border-white/10"
          initial={{ opacity: 0.7 }}
          whileHover={{ opacity: 1 }}
        >
          <Link 
            href={website} 
            target="_blank" 
            className="text-primary flex items-center text-sm hover:underline"
          >
            Visit Website
            <ExternalLink className="ml-1 h-3 w-3" />
          </Link>
        </motion.div>
      </div>
    </motion.div>
  );
}
