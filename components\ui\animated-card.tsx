'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'glow' | 'minimal';
  hoverEffect?: 'scale' | 'lift' | 'glow' | 'none';
  glowColor?: string;
  delay?: number;
}

export function AnimatedCard({
  children,
  className = '',
  variant = 'default',
  hoverEffect = 'scale',
  glowColor = 'rgba(var(--primary-rgb), 0.15)',
  delay = 0
}: AnimatedCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getVariantStyles = () => {
    switch (variant) {
      case 'glass':
        return 'border border-white/10 bg-black/30 backdrop-blur-md';
      case 'glow':
        return 'border border-primary/20 bg-black/40 backdrop-blur-sm shadow-lg shadow-primary/10';
      case 'minimal':
        return 'border border-border/30 bg-background/30 backdrop-blur-sm';
      default:
        return 'border border-border/50 bg-background/50 backdrop-blur-sm';
    }
  };

  const getHoverAnimation = () => {
    switch (hoverEffect) {
      case 'lift':
        return { y: -8, transition: { duration: 0.3 } };
      case 'glow':
        return { boxShadow: '0 20px 40px rgba(var(--primary-rgb), 0.3)', transition: { duration: 0.3 } };
      case 'none':
        return {};
      default:
        return { scale: 1.03, transition: { duration: 0.3 } };
    }
  };

  return (
    <motion.div
      className={`relative group ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      whileHover={getHoverAnimation()}
      transition={{ duration: 0.6, ease: "easeOut", delay }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`relative overflow-hidden transition-all duration-500 hover:border-primary/50 ${getVariantStyles()}`}>
        {children}

        {/* Animated gradient overlay */}
        <motion.div
          className="absolute inset-0 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.4 }}
          style={{
            background: `radial-gradient(circle at 50% 0%, ${glowColor}, transparent 70%)`,
          }}
        />

        {/* Animated border glow */}
        {variant === 'glow' && (
          <motion.div
            className="absolute inset-0 pointer-events-none rounded-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.4 }}
            style={{
              background: `linear-gradient(45deg, transparent, ${glowColor}, transparent)`,
              padding: '1px',
              mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
              maskComposite: 'exclude',
            }}
          />
        )}

        {/* Shimmer effect for glass variant */}
        {variant === 'glass' && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ x: '-100%' }}
            animate={{ x: isHovered ? '100%' : '-100%' }}
            transition={{ duration: 0.8, ease: 'easeInOut' }}
            style={{
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
              width: '50%',
            }}
          />
        )}
      </Card>
    </motion.div>
  );
}
