'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { Auth<PERSON>ard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { AuthButton } from '@/components/ui/auth-button';
import { CheckCircle, Circle, ArrowRight, BookOpen, Users, Target, MessageCircle } from 'lucide-react';

interface StepGuideProps {
  company: Company;
}

export function StepGuide({ company }: StepGuideProps) {
  const steps = [
    {
      id: 'platform',
      title: 'Platform Navigation',
      description: 'Learn how to navigate the InnoHub platform and access your resources',
      icon: <BookOpen className="h-6 w-6" />,
      completed: true,
      actions: [
        'Access your company dashboard',
        'Explore available courses and materials',
        'Set up your profile and preferences'
      ]
    },
    {
      id: 'mentors',
      title: 'Connect with <PERSON><PERSON>',
      description: 'Schedule meetings with your assigned mentors and advisors',
      icon: <Users className="h-6 w-6" />,
      completed: company.mentors.length > 0,
      actions: [
        'Review mentor profiles and expertise',
        'Schedule your first mentor meeting',
        'Prepare questions and goals for discussions'
      ]
    },
    {
      id: 'milestones',
      title: 'Track Your Milestones',
      description: 'Monitor your progress and upcoming deadlines',
      icon: <Target className="h-6 w-6" />,
      completed: company.milestones.some(m => m.status === 'completed'),
      actions: [
        'Review your milestone timeline',
        'Update progress on current tasks',
        'Plan for upcoming deliverables'
      ]
    },
    {
      id: 'community',
      title: 'Join the Community',
      description: 'Connect with other startups and participate in events',
      icon: <MessageCircle className="h-6 w-6" />,
      completed: false,
      actions: [
        'Join the Slack workspace',
        'Attend weekly startup meetups',
        'Participate in peer learning sessions'
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Getting Started Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle className="text-2xl">Getting Started Guide</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground mb-4">
              Welcome to the {company.programTrack.name}! Follow these steps to get the most out of your incubation experience.
            </p>
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-white mb-2">Program Duration</h4>
              <p className="text-sm text-muted-foreground">{company.programTrack.duration} • {company.programTrack.stage}</p>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Step Cards */}
      <div className="grid gap-6">
        {steps.map((step, index) => (
          <motion.div
            key={step.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <AuthCard className="group hover:border-primary/40 transition-colors duration-300">
              <AuthCardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* Step Icon and Status */}
                  <div className="flex flex-col items-center gap-2">
                    <div className={`p-3 rounded-full ${step.completed ? 'bg-green-500/20 border-green-500/30' : 'bg-primary/20 border-primary/30'} border`}>
                      {step.completed ? (
                        <CheckCircle className="h-6 w-6 text-green-400" />
                      ) : (
                        <div className="text-primary">{step.icon}</div>
                      )}
                    </div>
                    {index < steps.length - 1 && (
                      <div className="w-px h-16 bg-gradient-to-b from-primary/30 to-transparent" />
                    )}
                  </div>

                  {/* Step Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold text-white">{step.title}</h3>
                      {step.completed && (
                        <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">
                          Completed
                        </span>
                      )}
                    </div>
                    <p className="text-muted-foreground mb-4">{step.description}</p>

                    {/* Action Items */}
                    <div className="space-y-2 mb-4">
                      {step.actions.map((action, actionIndex) => (
                        <motion.div
                          key={actionIndex}
                          className="flex items-center gap-2 text-sm"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: (index * 0.1) + (actionIndex * 0.05) }}
                        >
                          <Circle className="h-3 w-3 text-primary" />
                          <span className="text-muted-foreground">{action}</span>
                        </motion.div>
                      ))}
                    </div>

                    {/* Action Button */}
                    {!step.completed && (
                      <AuthButton
                        variant="outline"
                        className="group-hover:border-primary/50 transition-colors duration-300"
                      >
                        Start Step
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </AuthButton>
                    )}
                  </div>
                </div>
              </AuthCardContent>
            </AuthCard>
          </motion.div>
        ))}
      </div>

      {/* Quick Access */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Quick Access</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <AuthButton variant="outline" className="justify-start">
                <BookOpen className="h-4 w-4 mr-2" />
                Course Catalog
              </AuthButton>
              <AuthButton variant="outline" className="justify-start">
                <Users className="h-4 w-4 mr-2" />
                Mentor Directory
              </AuthButton>
              <AuthButton variant="outline" className="justify-start">
                <MessageCircle className="h-4 w-4 mr-2" />
                Slack Workspace
              </AuthButton>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
