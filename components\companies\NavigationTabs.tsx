'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Tab {
  id: string;
  label: string;
  icon: string;
}

interface NavigationTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export function NavigationTabs({ tabs, activeTab, onTabChange }: NavigationTabsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="mb-8"
    >
      <div className="bg-black/40 backdrop-blur-md border border-primary/20 rounded-xl p-2">
        <div className="flex flex-wrap gap-2">
          {tabs.map((tab, index) => (
            <motion.button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                "relative flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200",
                "hover:bg-primary/10 hover:text-white",
                activeTab === tab.id
                  ? "bg-primary/20 text-white border border-primary/30"
                  : "text-muted-foreground"
              )}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Active indicator */}
              {activeTab === tab.id && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-lg"
                  layoutId="activeTab"
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                />
              )}
              
              {/* Content */}
              <span className="relative z-10 text-lg">{tab.icon}</span>
              <span className="relative z-10 hidden sm:inline">{tab.label}</span>
              
              {/* Mobile label */}
              <span className="relative z-10 sm:hidden text-xs">{tab.label}</span>
            </motion.button>
          ))}
        </div>
      </div>
    </motion.div>
  );
}
