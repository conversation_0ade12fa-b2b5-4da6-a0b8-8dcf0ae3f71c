'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AnimatedCard } from '@/components/ui/animated-card';
import { ArrowRight } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { ScrollAnimatedImage } from '@/components/ui/ScrollAnimatedImage';
import { useRef } from 'react';

interface Service {
  titleKey: string;
  descriptionKey: string;
  imageSrc: string;
  link: string;
}

interface ServiceCardItemProps {
  service: Service;
}

export const ServiceCardItem: React.FC<ServiceCardItemProps> = ({ service }) => {
  const { t } = useLanguage();
  const titleRef = useRef<HTMLHeadingElement>(null);

  return (
    <AnimatedCard key={service.titleKey} className="h-full relative">
      {service.imageSrc && titleRef.current && (
        <ScrollAnimatedImage
          src={service.imageSrc}
          alt={t(service.titleKey)}
          targetRef={titleRef}
        />
      )}
      <Card className="h-full flex flex-col">
        <CardHeader>
          <CardTitle ref={titleRef}>{t(service.titleKey)}</CardTitle>
        </CardHeader>
        <CardContent className="flex-grow pt-10"> {/* Added pt-10 to make space for image */}
          <CardDescription className="text-base">{t(service.descriptionKey)}</CardDescription>
        </CardContent>
        <CardFooter>
          <Button variant="ghost" className="group" asChild>
            <Link href={service.link}>
              {t('services.learnMore')}
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </AnimatedCard>
  );
};
