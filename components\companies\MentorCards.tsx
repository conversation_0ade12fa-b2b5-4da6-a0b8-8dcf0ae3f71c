'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { <PERSON><PERSON> } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { AuthButton } from '@/components/ui/auth-button';
import { Badge } from '@/components/ui/badge';
import { Mail, Linkedin, Calendar, Star } from 'lucide-react';

interface MentorCardsProps {
  mentors: <PERSON><PERSON>[];
}

export function MentorCards({ mentors }: MentorCardsProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle className="text-2xl">Your Mentors & Advisors</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground">
              Connect with your assigned mentors who will guide you through your entrepreneurial journey. 
              Each mentor brings unique expertise to help you overcome challenges and achieve your goals.
            </p>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Mentor Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {mentors.map((mentor, index) => (
          <motion.div
            key={mentor.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <AuthCard className="h-full group hover:border-primary/40 transition-colors duration-300">
              <AuthCardContent className="p-6">
                <div className="flex flex-col h-full">
                  {/* Mentor Header */}
                  <div className="flex items-start gap-4 mb-4">
                    <div className="relative">
                      <div className="w-16 h-16 rounded-full overflow-hidden bg-primary/20 border border-primary/30">
                        <Image
                          src={mentor.image}
                          alt={mentor.name}
                          width={64}
                          height={64}
                          className="object-cover w-full h-full"
                          onError={(e) => {
                            // Fallback to initials if image fails to load
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                              parent.innerHTML = `<div class="w-full h-full flex items-center justify-center text-primary font-semibold">${mentor.name.split(' ').map(n => n[0]).join('')}</div>`;
                            }
                          }}
                        />
                      </div>
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-black flex items-center justify-center">
                        <Star className="h-2.5 w-2.5 text-white" />
                      </div>
                    </div>

                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-1">{mentor.name}</h3>
                      <p className="text-primary text-sm font-medium mb-2">{mentor.role}</p>
                      
                      {/* Expertise Badges */}
                      <div className="flex flex-wrap gap-1">
                        {mentor.expertise.slice(0, 3).map((skill, skillIndex) => (
                          <Badge
                            key={skillIndex}
                            variant="secondary"
                            className="text-xs bg-primary/10 text-primary border-primary/20"
                          >
                            {skill}
                          </Badge>
                        ))}
                        {mentor.expertise.length > 3 && (
                          <Badge
                            variant="secondary"
                            className="text-xs bg-muted/20 text-muted-foreground border-muted/20"
                          >
                            +{mentor.expertise.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Bio */}
                  <div className="flex-1 mb-4">
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {mentor.bio}
                    </p>
                  </div>

                  {/* All Expertise */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-white mb-2">Areas of Expertise</h4>
                    <div className="flex flex-wrap gap-1">
                      {mentor.expertise.map((skill, skillIndex) => (
                        <Badge
                          key={skillIndex}
                          variant="secondary"
                          className="text-xs bg-primary/5 text-muted-foreground border-primary/10"
                        >
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Contact Actions */}
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      <AuthButton
                        variant="primary"
                        size="sm"
                        className="justify-center"
                        onClick={() => window.open(mentor.calendlyUrl, '_blank')}
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        Schedule Meeting
                      </AuthButton>
                      <AuthButton
                        variant="outline"
                        size="sm"
                        className="justify-center"
                        onClick={() => window.open(`mailto:${mentor.email}`, '_blank')}
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Send Email
                      </AuthButton>
                    </div>

                    {/* Social Links */}
                    {mentor.linkedin && (
                      <div className="flex justify-center">
                        <button
                          onClick={() => window.open(mentor.linkedin, '_blank')}
                          className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors duration-200"
                        >
                          <Linkedin className="h-4 w-4" />
                          Connect on LinkedIn
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </AuthCardContent>
            </AuthCard>
          </motion.div>
        ))}
      </div>

      {/* Meeting Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Making the Most of Mentor Meetings</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-white mb-2">Before the Meeting</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Prepare specific questions and challenges</li>
                  <li>• Share relevant materials in advance</li>
                  <li>• Set clear objectives for the session</li>
                </ul>
              </div>
              <div>
                <h4 className="text-sm font-medium text-white mb-2">During the Meeting</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Take detailed notes</li>
                  <li>• Ask for specific feedback and advice</li>
                  <li>• Discuss next steps and action items</li>
                </ul>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
