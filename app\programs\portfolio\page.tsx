'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { companies } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { Breadcrumb, breadcrumbConfigs } from '@/components/ui/breadcrumb';
import { Badge } from '@/components/ui/badge';
import { AuthButton } from '@/components/ui/auth-button';
import { Building2, Users, Target, Calendar, ExternalLink, BookOpen } from 'lucide-react';

export default function PortfolioPage() {
  const groupedCompanies = companies.reduce((acc, company) => {
    const trackId = company.programTrack.id;
    if (!acc[trackId]) {
      acc[trackId] = [];
    }
    acc[trackId].push(company);
    return acc;
  }, {} as Record<string, typeof companies>);

  return (
    <AnimatedGradientBackground className="min-h-screen">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Breadcrumb */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Breadcrumb items={breadcrumbConfigs.portfolio()} />
        </motion.div>

        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
            Our Portfolio Companies
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Meet the innovative startups and companies participating in our incubation programs.
            Each company receives personalized guidance and resources tailored to their unique journey.
          </p>
        </motion.div>

        {/* Program Tracks */}
        <div className="space-y-12">
          {Object.entries(groupedCompanies).map(([trackId, trackCompanies], trackIndex) => {
            const programTrack = trackCompanies[0].programTrack;
            
            return (
              <motion.div
                key={trackId}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: trackIndex * 0.2 }}
              >
                {/* Track Header */}
                <div className="mb-8">
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${programTrack.color} bg-opacity-20`}>
                      <span className="text-2xl">{programTrack.icon}</span>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">{programTrack.name}</h2>
                      <p className="text-muted-foreground">{programTrack.description}</p>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                      {programTrack.duration}
                    </Badge>
                    <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                      {programTrack.stage}
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                      {trackCompanies.length} Companies
                    </Badge>
                  </div>
                </div>

                {/* Companies Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {trackCompanies.map((company, companyIndex) => (
                    <motion.div
                      key={company.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: (trackIndex * 0.2) + (companyIndex * 0.1) }}
                    >
                      <AuthCard className="h-full group hover:border-primary/40 transition-colors duration-300">
                        <AuthCardContent className="p-6">
                          {/* Company Header */}
                          <div className="flex items-start gap-4 mb-4">
                            <div className="w-16 h-16 rounded-xl bg-white/10 border border-primary/20 flex items-center justify-center overflow-hidden">
                              {company.logo ? (
                                <Image
                                  src={company.logo}
                                  alt={`${company.name} logo`}
                                  width={48}
                                  height={48}
                                  className="object-contain"
                                />
                              ) : (
                                <Building2 className="h-8 w-8 text-primary" />
                              )}
                            </div>
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-white mb-1">{company.name}</h3>
                              <p className="text-sm text-primary">{company.industry}</p>
                              <Badge variant="secondary" className="mt-1 text-xs bg-green-500/20 text-green-400 border-green-500/30">
                                {company.currentStage}
                              </Badge>
                            </div>
                          </div>

                          {/* Description */}
                          <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                            {company.description}
                          </p>

                          {/* Stats */}
                          <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                            <div>
                              <div className="text-lg font-bold text-white">{company.progress.overall}%</div>
                              <div className="text-xs text-muted-foreground">Progress</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-white">{company.mentors.length}</div>
                              <div className="text-xs text-muted-foreground">Mentors</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-white">{company.milestones.length}</div>
                              <div className="text-xs text-muted-foreground">Milestones</div>
                            </div>
                          </div>

                          {/* Founders */}
                          <div className="mb-4">
                            <div className="flex items-center gap-2 mb-2">
                              <Users className="h-4 w-4 text-primary" />
                              <span className="text-sm font-medium text-white">Founders</span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {company.founders.map((founder) => (
                                <Badge
                                  key={founder}
                                  variant="secondary"
                                  className="text-xs bg-primary/10 text-muted-foreground border-primary/20"
                                >
                                  {founder}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Join Date */}
                          <div className="flex items-center gap-2 mb-4">
                            <Calendar className="h-4 w-4 text-primary" />
                            <span className="text-sm text-muted-foreground">
                              Joined {new Date(company.joinDate).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long'
                              })}
                            </span>
                          </div>

                          {/* Actions */}
                          <div className="space-y-2">
                            <Link href={`/companies/${company.id}/instructions`} className="block">
                              <AuthButton variant="primary" className="w-full justify-center">
                                <BookOpen className="h-4 w-4 mr-2" />
                                View Instructions
                              </AuthButton>
                            </Link>
                            
                            {company.website && (
                              <AuthButton
                                variant="outline"
                                className="w-full justify-center"
                                onClick={() => window.open(company.website, '_blank')}
                              >
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Visit Website
                              </AuthButton>
                            )}
                          </div>
                        </AuthCardContent>
                      </AuthCard>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Call to Action */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <AuthCard>
            <AuthCardHeader>
              <AuthCardTitle>Join Our Portfolio</AuthCardTitle>
            </AuthCardHeader>
            <AuthCardContent>
              <p className="text-muted-foreground mb-6">
                Ready to take your startup to the next level? Apply to one of our programs and join our growing portfolio of successful companies.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/programs">
                  <AuthButton variant="primary">
                    View Programs
                  </AuthButton>
                </Link>
                <Link href="/contact">
                  <AuthButton variant="outline">
                    Contact Us
                  </AuthButton>
                </Link>
              </div>
            </AuthCardContent>
          </AuthCard>
        </motion.div>
      </div>
    </AnimatedGradientBackground>
  );
}
