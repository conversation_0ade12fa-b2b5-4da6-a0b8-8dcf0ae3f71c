'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { TrendingUp, Target, Users, BarChart } from 'lucide-react';

interface MarketAnalysisProps {
  company: Company;
}

export function MarketAnalysis({ company }: MarketAnalysisProps) {
  return (
    <div className="space-y-6">
      {/* Market Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/20 rounded-lg">
                <TrendingUp className="h-5 w-5 text-primary" />
              </div>
              <AuthCardTitle>Market Overview</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
                <BarChart className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Market Size</h4>
                <p className="text-2xl font-bold text-green-400 mb-1">$2.5B</p>
                <p className="text-xs text-muted-foreground">AgriTech in Central Asia</p>
              </div>
              <div className="text-center p-4 bg-blue-500/5 border border-blue-500/20 rounded-lg">
                <Target className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Growth Rate</h4>
                <p className="text-2xl font-bold text-blue-400 mb-1">15%</p>
                <p className="text-xs text-muted-foreground">Annual CAGR</p>
              </div>
              <div className="text-center p-4 bg-purple-500/5 border border-purple-500/20 rounded-lg">
                <Users className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Target Farms</h4>
                <p className="text-2xl font-bold text-purple-400 mb-1">50K+</p>
                <p className="text-xs text-muted-foreground">Commercial farms in region</p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Competitive Landscape */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Competitive Landscape</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="space-y-4">
              {[
                { name: 'Traditional Farming Methods', share: '70%', status: 'Legacy' },
                { name: 'International AgriTech', share: '20%', status: 'Competitor' },
                { name: 'Local Tech Solutions', share: '8%', status: 'Competitor' },
                { name: 'EcoGrow', share: '2%', status: 'Us' }
              ].map((competitor, index) => (
                <motion.div
                  key={index}
                  className="flex items-center justify-between p-3 bg-primary/5 border border-primary/20 rounded-lg"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 + index * 0.1 }}
                >
                  <div>
                    <p className="font-medium text-white">{competitor.name}</p>
                    <p className="text-sm text-muted-foreground">{competitor.status}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-primary">{competitor.share}</p>
                    <p className="text-xs text-muted-foreground">Market Share</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Market Strategy */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Market Strategy</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-white mb-3">Go-to-Market Strategy</h4>
                <div className="space-y-2">
                  {[
                    'Direct sales to large commercial farms',
                    'Partnerships with agricultural cooperatives',
                    'Government pilot programs',
                    'Trade show and conference presence'
                  ].map((strategy, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                      <p className="text-sm text-muted-foreground">{strategy}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-white mb-3">Market Positioning</h4>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Positioned as the leading localized AgriTech solution for harsh climate conditions, 
                  offering integrated hardware-software approach with AI-driven insights specifically 
                  designed for Central Asian agricultural challenges.
                </p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
