'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Leaf, Users, Heart, Globe, Recycle, Award } from 'lucide-react';

interface SocialImpactProps {
  company: Company;
}

export function SocialImpact({ company }: SocialImpactProps) {
  const impactMetrics = [
    { label: 'Water Saved', value: '2M Liters', description: 'Through smart irrigation', icon: <Leaf className="h-6 w-6" /> },
    { label: 'Farmers Helped', value: '150+', description: 'Across Mongolia', icon: <Users className="h-6 w-6" /> },
    { label: 'CO2 Reduced', value: '500 Tons', description: 'Annual reduction', icon: <Globe className="h-6 w-6" /> },
    { label: 'Food Security', value: '40%', description: 'Yield improvement', icon: <Heart className="h-6 w-6" /> }
  ];

  return (
    <div className="space-y-6">
      {/* Impact Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <Leaf className="h-5 w-5 text-green-400" />
              </div>
              <AuthCardTitle>Environmental & Social Impact</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground mb-6">
              {company.name} is committed to creating positive environmental and social impact through 
              sustainable agriculture technology that benefits farmers, communities, and the planet.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {impactMetrics.map((metric, index) => (
                <motion.div
                  key={index}
                  className="text-center p-4 bg-green-500/5 border border-green-500/20 rounded-lg"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="text-green-400 mx-auto mb-2">{metric.icon}</div>
                  <p className="text-xl font-bold text-green-400 mb-1">{metric.value}</p>
                  <p className="text-sm font-medium text-white mb-1">{metric.label}</p>
                  <p className="text-xs text-muted-foreground">{metric.description}</p>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Sustainability Efforts */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Recycle className="h-5 w-5 text-blue-400" />
              </div>
              <AuthCardTitle>Sustainability Initiatives</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-white mb-3">Environmental Efforts</h4>
                <div className="space-y-3">
                  {[
                    'Solar-powered IoT devices reduce energy consumption',
                    'Water conservation through precision irrigation',
                    'Reduced chemical fertilizer usage via soil monitoring',
                    'Carbon footprint tracking and reduction programs'
                  ].map((effort, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                      <p className="text-sm text-muted-foreground">{effort}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-white mb-3">Social Responsibility</h4>
                <div className="space-y-3">
                  {[
                    'Training programs for smallholder farmers',
                    'Affordable pricing for developing communities',
                    'Local employment and skill development',
                    'Knowledge sharing with agricultural institutions'
                  ].map((responsibility, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                      <p className="text-sm text-muted-foreground">{responsibility}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Community Involvement */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Users className="h-5 w-5 text-purple-400" />
              </div>
              <AuthCardTitle>Community Involvement</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="space-y-4">
              <div className="p-4 bg-purple-500/5 border border-purple-500/20 rounded-lg">
                <h4 className="font-medium text-white mb-2">Agricultural Education Program</h4>
                <p className="text-sm text-muted-foreground">
                  Partnership with local universities to provide hands-on training in modern farming 
                  techniques and technology adoption for the next generation of farmers.
                </p>
              </div>
              <div className="p-4 bg-blue-500/5 border border-blue-500/20 rounded-lg">
                <h4 className="font-medium text-white mb-2">Rural Development Initiative</h4>
                <p className="text-sm text-muted-foreground">
                  Supporting rural communities through technology access, digital literacy programs, 
                  and economic development opportunities in agricultural regions.
                </p>
              </div>
              <div className="p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
                <h4 className="font-medium text-white mb-2">Food Security Project</h4>
                <p className="text-sm text-muted-foreground">
                  Collaborating with government agencies and NGOs to improve food security through 
                  increased agricultural productivity and sustainable farming practices.
                </p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* ESG Commitments */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/20 rounded-lg">
                <Award className="h-5 w-5 text-orange-400" />
              </div>
              <AuthCardTitle>ESG Commitments</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
                <Globe className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Environmental</h4>
                <p className="text-sm text-muted-foreground">Carbon neutral operations by 2025</p>
              </div>
              <div className="text-center p-4 bg-blue-500/5 border border-blue-500/20 rounded-lg">
                <Users className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Social</h4>
                <p className="text-sm text-muted-foreground">50% women in leadership by 2026</p>
              </div>
              <div className="text-center p-4 bg-purple-500/5 border border-purple-500/20 rounded-lg">
                <Award className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <h4 className="font-medium text-white mb-1">Governance</h4>
                <p className="text-sm text-muted-foreground">Transparent reporting standards</p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
