'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { getCurrentUser, markUserAsOnboarded } from '@/lib/auth';
import { 
  Brain, 
  Lightbulb, 
  Users, 
  Target, 
  BookOpen, 
  ArrowRight, 
  RefreshCw,
  TrendingUp,
  Award,
  Briefcase
} from 'lucide-react';

interface TalentScores {
  analytical: number;
  creative: number;
  social: number;
  strategic: number;
}

interface TalentType {
  key: string;
  icon: React.ReactNode;
  color: string;
  courses: string[];
  careers: string[];
}

const talentTypes: Record<string, TalentType> = {
  analytical: {
    key: 'analytical',
    icon: <Brain className="h-6 w-6" />,
    color: 'text-blue-400',
    courses: ['Data Analysis', 'Programming Fundamentals', 'Financial Modeling', 'Research Methods'],
    careers: ['Data Scientist', 'Software Engineer', 'Financial Analyst', 'Research Scientist']
  },
  creative: {
    key: 'creative',
    icon: <Lightbulb className="h-6 w-6" />,
    color: 'text-purple-400',
    courses: ['Design Thinking', 'Digital Marketing', 'Content Creation', 'Innovation Management'],
    careers: ['UX/UI Designer', 'Marketing Manager', 'Content Creator', 'Product Designer']
  },
  social: {
    key: 'social',
    icon: <Users className="h-6 w-6" />,
    color: 'text-green-400',
    courses: ['Leadership Skills', 'Communication', 'Team Management', 'Public Speaking'],
    careers: ['Project Manager', 'HR Manager', 'Sales Manager', 'Community Manager']
  },
  strategic: {
    key: 'strategic',
    icon: <Target className="h-6 w-6" />,
    color: 'text-orange-400',
    courses: ['Business Strategy', 'Entrepreneurship', 'Operations Management', 'Strategic Planning'],
    careers: ['Business Analyst', 'Entrepreneur', 'Operations Manager', 'Strategy Consultant']
  }
};

export default function ResultsPage() {
  const { t, isLoaded } = useLanguage();
  const router = useRouter();
  const [scores, setScores] = useState<TalentScores | null>(null);
  const [primaryTalent, setPrimaryTalent] = useState<string>('');
  const [secondaryTalents, setSecondaryTalents] = useState<string[]>([]);
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    // Check if user has completed assessment
    const onboardingCompleted = localStorage.getItem('onboardingCompleted');
    const assessmentResults = localStorage.getItem('assessmentResults');
    
    if (!onboardingCompleted || !assessmentResults) {
      router.push('/onboarding');
      return;
    }

    try {
      const results: TalentScores = JSON.parse(assessmentResults);
      setScores(results);
      
      // Determine primary and secondary talents
      const sortedTalents = Object.entries(results)
        .sort(([,a], [,b]) => b - a)
        .map(([talent]) => talent);
      
      setPrimaryTalent(sortedTalents[0]);
      setSecondaryTalents(sortedTalents.slice(1, 3));
    } catch (error) {
      console.error('Error parsing assessment results:', error);
      router.push('/onboarding');
    }
  }, [router]);

  const handleContinueToCourses = () => {
    setIsNavigating(true);

    // Mark user as onboarded using the auth system
    markUserAsOnboarded();

    // Small delay to ensure localStorage/cookies are set
    setTimeout(() => {
      // Force a page reload to ensure middleware picks up the new cookie
      window.location.href = '/courses';
    }, 100);
  };

  const handleRetakeAssessment = () => {
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('onboardingCompleted');
    router.push('/onboarding');
  };

  if (!isLoaded || !scores) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
          <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
          <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
        </div>
      </div>
    );
  }

  const primaryTalentData = talentTypes[primaryTalent];
  const maxScore = Math.max(...Object.values(scores));

  return (
    <AnimatedGradientBackground className="min-h-screen py-20">
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="rounded-full bg-primary/10 p-4">
              <Award className="h-12 w-12 text-primary" />
            </div>
          </div>
          <h1 className="text-3xl font-bold mb-2">{t('results.title')}</h1>
          <p className="text-muted-foreground text-lg">{t('results.subtitle')}</p>
        </motion.div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Primary Talent */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className={`${primaryTalentData.color}`}>
                    {primaryTalentData.icon}
                  </div>
                  {t('results.primaryTalent')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <h3 className="text-xl font-semibold mb-2">
                    {t(`talent.${primaryTalent}`)}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {t(`talent.${primaryTalent}.desc`)}
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Strength Level</span>
                    <span>{Math.round((scores[primaryTalent as keyof TalentScores] / maxScore) * 100)}%</span>
                  </div>
                  <Progress 
                    value={(scores[primaryTalent as keyof TalentScores] / maxScore) * 100} 
                    className="h-3"
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Secondary Talents */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  {t('results.secondaryTalents')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {secondaryTalents.map((talent, index) => {
                  const talentData = talentTypes[talent];
                  return (
                    <div key={talent} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className={`${talentData.color}`}>
                          {talentData.icon}
                        </div>
                        <span className="font-medium">{t(`talent.${talent}`)}</span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Level</span>
                          <span>{Math.round((scores[talent as keyof TalentScores] / maxScore) * 100)}%</span>
                        </div>
                        <Progress 
                          value={(scores[talent as keyof TalentScores] / maxScore) * 100} 
                          className="h-2"
                        />
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          </motion.div>

          {/* Recommended Courses */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <BookOpen className="h-5 w-5 text-primary" />
                  {t('results.recommendedCourses')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {primaryTalentData.courses.map((course, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 rounded-lg bg-primary/5">
                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                      <span className="text-sm">{course}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Career Paths */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <Briefcase className="h-5 w-5 text-primary" />
                  {t('results.careerPaths')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {primaryTalentData.careers.map((career, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 rounded-lg bg-primary/5">
                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                      <span className="text-sm">{career}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center mt-8"
        >
          <Button
            onClick={handleContinueToCourses}
            disabled={isNavigating}
            className="bg-primary hover:bg-primary/90 flex items-center gap-2"
            size="lg"
          >
            {isNavigating ? (
              <>
                <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                {t('courses.loading') || 'Loading...'}
              </>
            ) : (
              <>
                <BookOpen className="h-5 w-5" />
                {t('results.continueToCourses')}
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </Button>
          
          <Button
            onClick={handleRetakeAssessment}
            variant="outline"
            className="border-primary/20 hover:bg-primary/10 flex items-center gap-2"
            size="lg"
          >
            <RefreshCw className="h-4 w-4" />
            {t('results.retakeAssessment')}
          </Button>
        </motion.div>
      </div>
    </AnimatedGradientBackground>
  );
}
