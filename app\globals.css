@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.627 0.265 303.9); /* Purple */
  --primary-foreground: oklch(0.985 0 0); /* White */
  --secondary: oklch(0.2 0 0); /* Black */
  --secondary-foreground: oklch(0.985 0 0); /* White */
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.627 0.265 303.9); /* Purple */
  --chart-2: oklch(0.05 0 0); /* Black */
  --chart-3: oklch(0.985 0 0); /* White */
  --chart-4: oklch(0.7 0.2 303.9); /* Light Purple */
  --chart-5: oklch(0.4 0.2 303.9); /* Dark Purple */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.627 0.265 303.9); /* Purple */
  --sidebar-primary-foreground: oklch(0.985 0 0); /* White */
  --sidebar-accent: oklch(0.05 0 0); /* Black */
  --sidebar-accent-foreground: oklch(0.985 0 0); /* White */
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.627 0.265 303.9); /* Purple */

  /* For Aceternity UI components */
  --primary-rgb: 128, 90, 213; /* Purple in RGB */
}

.dark {
  --background: oklch(0.05 0 0); /* Pure black */
  --foreground: oklch(0.985 0 0); /* White */
  --card: oklch(0.1 0 0 / 50%); /* Semi-transparent dark */
  --card-foreground: oklch(0.985 0 0); /* White */
  --popover: oklch(0.1 0 0 / 90%); /* Semi-transparent dark */
  --popover-foreground: oklch(0.985 0 0); /* White */
  --primary: oklch(0.627 0.265 303.9); /* Purple */
  --primary-foreground: oklch(0.05 0 0); /* Black */
  --secondary: oklch(0.05 0 0); /* Black */
  --secondary-foreground: oklch(0.985 0 0); /* White */
  --muted: oklch(0.2 0 0); /* Dark gray */
  --muted-foreground: oklch(0.7 0 0); /* Light gray */
  --accent: oklch(0.2 0 0); /* Dark gray */
  --accent-foreground: oklch(0.985 0 0); /* White */
  --destructive: oklch(0.704 0.191 22.216); /* Red */
  --border: oklch(1 0 0 / 10%); /* Semi-transparent white */
  --input: oklch(1 0 0 / 15%); /* Semi-transparent white */
  --ring: oklch(0.627 0.265 303.9 / 50%); /* Semi-transparent purple */
  --chart-1: oklch(0.627 0.265 303.9); /* Purple */
  --chart-2: oklch(0.05 0 0); /* Black */
  --chart-3: oklch(0.985 0 0); /* White */
  --chart-4: oklch(0.7 0.2 303.9); /* Light Purple */
  --chart-5: oklch(0.4 0.2 303.9); /* Dark Purple */
  --sidebar: oklch(0.05 0 0); /* Black */
  --sidebar-foreground: oklch(0.985 0 0); /* White */
  --sidebar-primary: oklch(0.627 0.265 303.9); /* Purple */
  --sidebar-primary-foreground: oklch(0.985 0 0); /* White */
  --sidebar-accent: oklch(0.2 0 0); /* Dark gray */
  --sidebar-accent-foreground: oklch(0.985 0 0); /* White */
  --sidebar-border: oklch(1 0 0 / 10%); /* Semi-transparent white */
  --sidebar-ring: oklch(0.627 0.265 303.9 / 50%); /* Semi-transparent purple */

  /* For Aceternity UI components */
  --primary-rgb: 128, 90, 213; /* Purple in RGB */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
