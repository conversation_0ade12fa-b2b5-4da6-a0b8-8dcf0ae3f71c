'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { CompanyOverview } from './profile/CompanyOverview';
import { FoundersLeadership } from './profile/FoundersLeadership';
import { ProductService } from './profile/ProductService';
import { MarketAnalysis } from './profile/MarketAnalysis';
import { FinancialInfo } from './profile/FinancialInfo';
import { CompanyTimeline } from './profile/CompanyTimeline';
import { TechnologyInnovation } from './profile/TechnologyInnovation';
import { SocialImpact } from './profile/SocialImpact';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';

interface CompanyProfileProps {
  company: Company;
}

export function CompanyProfile({ company }: CompanyProfileProps) {
  const [activeSection, setActiveSection] = useState('overview');

  const sections = [
    { id: 'overview', label: 'Overview', icon: '🏢' },
    { id: 'founders', label: 'Founders & Leadership', icon: '👥' },
    { id: 'product', label: 'Product & Services', icon: '🚀' },
    { id: 'market', label: 'Market Analysis', icon: '📊' },
    { id: 'financial', label: 'Financial Info', icon: '💰' },
    { id: 'timeline', label: 'Company Timeline', icon: '📅' },
    { id: 'technology', label: 'Technology', icon: '⚡' },
    { id: 'impact', label: 'Social Impact', icon: '🌱' }
  ];

  const renderSection = () => {
    if (!company.profile) {
      return (
        <AuthCard>
          <AuthCardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              Detailed company profile information is being prepared and will be available soon.
            </p>
          </AuthCardContent>
        </AuthCard>
      );
    }

    switch (activeSection) {
      case 'overview':
        return <CompanyOverview company={company} />;
      case 'founders':
        return <FoundersLeadership company={company} />;
      case 'product':
        return <ProductService company={company} />;
      case 'market':
        return <MarketAnalysis company={company} />;
      case 'financial':
        return <FinancialInfo company={company} />;
      case 'timeline':
        return <CompanyTimeline company={company} />;
      case 'technology':
        return <TechnologyInnovation company={company} />;
      case 'impact':
        return <SocialImpact company={company} />;
      default:
        return <CompanyOverview company={company} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center justify-between">
              <AuthCardTitle className="text-2xl">Company Profile</AuthCardTitle>
              <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                Comprehensive Overview
              </Badge>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground">
              Explore detailed information about {company.name}, including company history, 
              leadership team, products, market analysis, and more.
            </p>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Section Navigation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="bg-black/40 backdrop-blur-md border border-primary/20 rounded-xl p-2">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2">
            {sections.map((section, index) => (
              <motion.button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`flex flex-col items-center gap-1 p-3 rounded-lg text-xs font-medium transition-all duration-200 ${
                  activeSection === section.id
                    ? 'bg-primary/20 text-white border border-primary/30'
                    : 'text-muted-foreground hover:bg-primary/10 hover:text-white'
                }`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-lg">{section.icon}</span>
                <span className="text-center leading-tight">{section.label}</span>
              </motion.button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Section Content */}
      <motion.div
        key={activeSection}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {renderSection()}
      </motion.div>
    </div>
  );
}
