'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import Image from 'next/image';

interface ScrollAnimatedImageProps {
  src: string;
  alt: string;
  targetRef: React.RefObject<HTMLElement | null>; // Allow null for initial ref value
}

export const ScrollAnimatedImage: React.FC<ScrollAnimatedImageProps> = ({ src, alt, targetRef }) => {
  const imageRef = useRef<HTMLDivElement>(null);
  const [targetPosition, setTargetPosition] = useState({ x: 0, y: 0 });

  // Get scroll progress relative to the container of the targetRef or a specific section
  // For simplicity, we'll use window scroll here, but for more complex layouts,
  // you might need to use useScroll with a target ref for the section.
  const { scrollYProgress } = useScroll();

  // Update target position when targetRef is available and on resize
  useEffect(() => {
    const updatePosition = () => {
      if (targetRef.current) {
        const rect = targetRef.current.getBoundingClientRect();
        // Calculate center of the target element relative to the viewport
        setTargetPosition({
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2,
        });
      }
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);
    return () => window.removeEventListener('resize', updatePosition);
  }, [targetRef]);

  // Define the circular/elliptical path
  // These values might need tweaking based on image size and desired effect
  const radiusX = 30; // Horizontal radius of the ellipse
  const radiusY = 20; // Vertical radius of the ellipse
  const speedFactor = 2; // Adjusts how quickly the image moves through the path

  // Transform scroll progress into x and y coordinates for the image
  // This creates an elliptical path.
  const x = useTransform(scrollYProgress, [0, 1], [
    targetPosition.x + radiusX * Math.cos(0),
    targetPosition.x + radiusX * Math.cos(speedFactor * 2 * Math.PI)
  ]);
  const y = useTransform(scrollYProgress, [0, 1], [
    targetPosition.y + radiusY * Math.sin(0),
    targetPosition.y + radiusY * Math.sin(speedFactor * 2 * Math.PI)
  ]);

  // A simpler up-and-down motion if the circular path is too complex to start
  // const y = useTransform(scrollYProgress, [0, 1], [targetPosition.y - 20, targetPosition.y + 20]);


  if (!src) return null;

  return (
    <motion.div
      ref={imageRef}
      style={{
        position: 'absolute', // Or 'fixed' if you want it relative to viewport
        x: x, // Apply the transformed x value
        y: y, // Apply the transformed y value
        left: 0, // Adjust these if image is not centered as expected
        top: 0,
        zIndex: 10, // Ensure it's above other elements if needed
      }}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Image src={src} alt={alt} width={80} height={80} className="object-contain" />
    </motion.div>
  );
};
