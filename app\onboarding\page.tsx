'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { ChevronLeft, ChevronRight, Sparkles, Brain, Target, Users } from 'lucide-react';

interface AssessmentQuestion {
  id: string;
  questionKey: string;
  options: {
    key: string;
    value: string;
    weight: {
      analytical: number;
      creative: number;
      social: number;
      strategic: number;
    };
  }[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'q1',
    questionKey: 'assessment.q1',
    options: [
      { key: 'assessment.q1.a', value: 'analytical', weight: { analytical: 3, creative: 0, social: 0, strategic: 1 } },
      { key: 'assessment.q1.b', value: 'creative', weight: { analytical: 0, creative: 3, social: 1, strategic: 0 } },
      { key: 'assessment.q1.c', value: 'social', weight: { analytical: 0, creative: 1, social: 3, strategic: 0 } },
      { key: 'assessment.q1.d', value: 'strategic', weight: { analytical: 1, creative: 0, social: 0, strategic: 3 } },
    ],
  },
  {
    id: 'q2',
    questionKey: 'assessment.q2',
    options: [
      { key: 'assessment.q2.a', value: 'analytical', weight: { analytical: 2, creative: 1, social: 0, strategic: 1 } },
      { key: 'assessment.q2.b', value: 'creative', weight: { analytical: 1, creative: 3, social: 0, strategic: 0 } },
      { key: 'assessment.q2.c', value: 'social', weight: { analytical: 0, creative: 1, social: 3, strategic: 1 } },
      { key: 'assessment.q2.d', value: 'strategic', weight: { analytical: 2, creative: 0, social: 0, strategic: 2 } },
    ],
  },
  {
    id: 'q3',
    questionKey: 'assessment.q3',
    options: [
      { key: 'assessment.q3.a', value: 'analytical', weight: { analytical: 3, creative: 1, social: 0, strategic: 0 } },
      { key: 'assessment.q3.b', value: 'creative', weight: { analytical: 0, creative: 3, social: 0, strategic: 1 } },
      { key: 'assessment.q3.c', value: 'social', weight: { analytical: 0, creative: 0, social: 3, strategic: 1 } },
      { key: 'assessment.q3.d', value: 'strategic', weight: { analytical: 1, creative: 0, social: 1, strategic: 3 } },
    ],
  },
  {
    id: 'q4',
    questionKey: 'assessment.q4',
    options: [
      { key: 'assessment.q4.a', value: 'analytical', weight: { analytical: 3, creative: 1, social: 0, strategic: 1 } },
      { key: 'assessment.q4.b', value: 'creative', weight: { analytical: 0, creative: 3, social: 1, strategic: 0 } },
      { key: 'assessment.q4.c', value: 'social', weight: { analytical: 0, creative: 0, social: 3, strategic: 1 } },
      { key: 'assessment.q4.d', value: 'strategic', weight: { analytical: 1, creative: 1, social: 1, strategic: 3 } },
    ],
  },
  {
    id: 'q5',
    questionKey: 'assessment.q5',
    options: [
      { key: 'assessment.q5.a', value: 'analytical', weight: { analytical: 2, creative: 2, social: 0, strategic: 1 } },
      { key: 'assessment.q5.b', value: 'social', weight: { analytical: 1, creative: 1, social: 3, strategic: 0 } },
      { key: 'assessment.q5.c', value: 'strategic', weight: { analytical: 0, creative: 0, social: 2, strategic: 3 } },
      { key: 'assessment.q5.d', value: 'analytical', weight: { analytical: 1, creative: 0, social: 1, strategic: 2 } },
    ],
  },
];

export default function OnboardingPage() {
  const { t, isLoaded } = useLanguage();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const totalSteps = assessmentQuestions.length;
  const progress = ((currentStep + 1) / totalSteps) * 100;

  const handleAnswerSelect = (questionId: string, answer: string) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const calculateResults = () => {
    const scores = { analytical: 0, creative: 0, social: 0, strategic: 0 };
    
    Object.entries(answers).forEach(([questionId, selectedValue]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const selectedOption = question?.options.find(opt => opt.value === selectedValue);
      
      if (selectedOption) {
        scores.analytical += selectedOption.weight.analytical;
        scores.creative += selectedOption.weight.creative;
        scores.social += selectedOption.weight.social;
        scores.strategic += selectedOption.weight.strategic;
      }
    });

    return scores;
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Calculate assessment results
    const results = calculateResults();
    
    // Store results in localStorage (in a real app, this would be sent to a backend)
    localStorage.setItem('assessmentResults', JSON.stringify(results));
    localStorage.setItem('onboardingCompleted', 'true');
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      router.push('/onboarding/results');
    }, 2000);
  };

  const currentQuestion = assessmentQuestions[currentStep];
  const currentAnswer = answers[currentQuestion?.id];

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
          <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
          <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <AnimatedGradientBackground className="min-h-screen flex items-center justify-center py-20">
      <div className="w-full max-w-2xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="rounded-full bg-primary/10 p-3">
                  <Brain className="h-8 w-8 text-primary" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold">
                {t('onboarding.talentAssessment')}
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                {t('onboarding.assessmentDesc')}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{t('onboarding.question')} {currentStep + 1} {t('onboarding.of')} {totalSteps}</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* Question */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-4"
                >
                  <h3 className="text-lg font-semibold">
                    {t(currentQuestion.questionKey)}
                  </h3>
                  
                  <div className="space-y-3">
                    {currentQuestion.options.map((option, index) => (
                      <motion.button
                        key={option.key}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        onClick={() => handleAnswerSelect(currentQuestion.id, option.value)}
                        className={`w-full p-4 text-left rounded-lg border transition-all ${
                          currentAnswer === option.value
                            ? 'border-primary bg-primary/10 text-primary'
                            : 'border-primary/20 hover:border-primary/40 hover:bg-primary/5'
                        }`}
                      >
                        {t(option.key)}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              </AnimatePresence>

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                  {t('onboarding.previous')}
                </Button>
                
                <Button
                  onClick={handleNext}
                  disabled={!currentAnswer || isSubmitting}
                  className="flex items-center gap-2 bg-primary hover:bg-primary/90"
                >
                  {isSubmitting ? (
                    <>
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      {t('onboarding.submitting')}
                    </>
                  ) : currentStep === totalSteps - 1 ? (
                    <>
                      <Sparkles className="h-4 w-4" />
                      {t('onboarding.submit')}
                    </>
                  ) : (
                    <>
                      {t('onboarding.next')}
                      <ChevronRight className="h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AnimatedGradientBackground>
  );
}
