'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';

const projects = [
  {
    id: 1,
    title: 'AI Chatbot',
    category: 'Natural Language Processing',
    technologies: ['Google Cloud', 'Gemini'],
    image: '/placeholder-1.jpg',
    link: '/projects/chatbot',
    description: 'An intelligent chatbot that provides instant customer support and information retrieval.',
  },
  {
    id: 2,
    title: 'Predictive Analytics',
    category: 'Machine Learning',
    technologies: ['Python', 'Docker'],
    image: '/placeholder-2.jpg',
    link: '/projects/predictions',
    description: 'Advanced predictive models that forecast business trends and customer behavior.',
  },
  {
    id: 3,
    title: 'Expense Management Assistant',
    category: 'Generative AI',
    technologies: ['ChatGPT', 'Copilot Studio'],
    image: '/placeholder-3.jpg',
    link: '/projects/expense-assistant',
    description: 'AI-powered assistant that automates expense tracking and reporting processes.',
  },
  {
    id: 4,
    title: 'Document Analysis (OCR)',
    category: 'Computer Vision',
    technologies: ['Tesseract', 'Microsoft AI'],
    image: '/placeholder-4.jpg',
    link: '/projects/ocr',
    description: 'Intelligent document processing system that extracts and analyzes information from various document types.',
  },
  {
    id: 5,
    title: 'Image Generation',
    category: 'Generative AI',
    technologies: ['Stable Diffusion', 'DALL-E'],
    image: '/placeholder-5.jpg',
    link: '/projects/image-generation',
    description: 'Custom image generation system for creating consistent brand visuals and marketing materials.',
  },
  {
    id: 6,
    title: 'Content Creation',
    category: 'Natural Language Processing',
    technologies: ['GPT-4', 'Claude'],
    image: '/placeholder-6.jpg',
    link: '/projects/content-creation',
    description: 'AI-powered content generation platform for creating engaging marketing materials.',
  },
  {
    id: 7,
    title: 'Data Analysis',
    category: 'Data Science',
    technologies: ['R', 'Python'],
    image: '/placeholder-7.jpg',
    link: '/projects/data-analysis',
    description: 'Comprehensive data analysis solution for extracting actionable insights from complex datasets.',
  },
  {
    id: 8,
    title: 'Recommendation Engine',
    category: 'Machine Learning',
    technologies: ['TensorFlow', 'PyTorch'],
    image: '/placeholder-8.jpg',
    link: '/projects/recommendation-engine',
    description: 'Personalized recommendation system that enhances user experience and increases engagement.',
  },
];

const categories = [
  'All',
  'Natural Language Processing',
  'Machine Learning',
  'Computer Vision',
  'Generative AI',
  'Data Science',
];

export default function ProjectsPage() {
  const [activeCategory, setActiveCategory] = useState('All');
  
  const filteredProjects = activeCategory === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeCategory);

  return (
    <>
      <AnimatedGradientBackground className="min-h-[40vh] flex flex-col items-center justify-center pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4"
            >
              Our Projects
            </motion.div>
            
            <AnimatedText
              text="Innovative AI Solutions in Action"
              className="text-4xl md:text-5xl font-bold tracking-tight mb-6"
            />
            
            <AnimatedText
              text="Explore our portfolio of successful AI implementations across various industries and use cases."
              className="text-xl text-muted-foreground"
              once
            />
          </div>
        </div>
      </AnimatedGradientBackground>
      
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={activeCategory === category ? "default" : "outline"}
                onClick={() => setActiveCategory(category)}
                className="mb-2"
              >
                {category}
              </Button>
            ))}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="h-full"
              >
                <Link href={project.link} className="h-full block">
                  <Card className="overflow-hidden h-full flex flex-col border-border/50 bg-background/50 backdrop-blur-sm transition-all duration-300 hover:border-primary/50 hover:shadow-lg">
                    <div className="relative aspect-[4/3] bg-muted">
                      <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                        {project.id.toString().padStart(3, '0')}
                      </div>
                    </div>
                    <CardContent className="flex-grow pt-6">
                      <h3 className="font-semibold text-xl mb-2">{project.title}</h3>
                      <p className="text-muted-foreground text-sm mb-4">{project.category}</p>
                      <p className="text-sm">{project.description}</p>
                    </CardContent>
                    <CardFooter className="flex flex-wrap gap-2 pt-0 pb-6">
                      {project.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary/10 text-primary hover:bg-primary/20"
                        >
                          {tech}
                        </span>
                      ))}
                    </CardFooter>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
