'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Badge } from '@/components/ui/badge';
import { Target, Eye, Heart, Briefcase, Users, Trophy } from 'lucide-react';

interface CompanyOverviewProps {
  company: Company;
}

export function CompanyOverview({ company }: CompanyOverviewProps) {
  if (!company.profile?.overview) {
    return (
      <AuthCard>
        <AuthCardContent className="p-8 text-center">
          <p className="text-muted-foreground">Company overview information is being prepared.</p>
        </AuthCardContent>
      </AuthCard>
    );
  }

  const { overview } = company.profile;

  return (
    <div className="space-y-6">
      {/* Founding Story */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/20 rounded-lg">
                <Briefcase className="h-5 w-5 text-primary" />
              </div>
              <AuthCardTitle>Founding Story</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground leading-relaxed">
              {overview.foundingStory}
            </p>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Mission & Vision */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <AuthCard className="h-full">
            <AuthCardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <Target className="h-5 w-5 text-green-400" />
                </div>
                <AuthCardTitle>Mission</AuthCardTitle>
              </div>
            </AuthCardHeader>
            <AuthCardContent>
              <p className="text-muted-foreground leading-relaxed">
                {overview.mission}
              </p>
            </AuthCardContent>
          </AuthCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <AuthCard className="h-full">
            <AuthCardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Eye className="h-5 w-5 text-blue-400" />
                </div>
                <AuthCardTitle>Vision</AuthCardTitle>
              </div>
            </AuthCardHeader>
            <AuthCardContent>
              <p className="text-muted-foreground leading-relaxed">
                {overview.vision}
              </p>
            </AuthCardContent>
          </AuthCard>
        </motion.div>
      </div>

      {/* Core Values */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Heart className="h-5 w-5 text-purple-400" />
              </div>
              <AuthCardTitle>Core Values</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {overview.coreValues.map((value, index) => (
                <motion.div
                  key={index}
                  className="flex items-start gap-3 p-4 bg-primary/5 border border-primary/20 rounded-lg"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                >
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <p className="text-sm text-muted-foreground leading-relaxed">{value}</p>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Business Model & Value Proposition */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <AuthCard className="h-full">
            <AuthCardHeader>
              <AuthCardTitle>Business Model</AuthCardTitle>
            </AuthCardHeader>
            <AuthCardContent>
              <p className="text-muted-foreground leading-relaxed">
                {overview.businessModel}
              </p>
            </AuthCardContent>
          </AuthCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <AuthCard className="h-full">
            <AuthCardHeader>
              <AuthCardTitle>Value Proposition</AuthCardTitle>
            </AuthCardHeader>
            <AuthCardContent>
              <p className="text-muted-foreground leading-relaxed">
                {overview.valueProposition}
              </p>
            </AuthCardContent>
          </AuthCard>
        </motion.div>
      </div>

      {/* Target Market & Customer Segments */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/20 rounded-lg">
                <Users className="h-5 w-5 text-orange-400" />
              </div>
              <AuthCardTitle>Target Market & Customer Segments</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-white mb-2">Target Market</h4>
                <p className="text-muted-foreground leading-relaxed">{overview.targetMarket}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-white mb-3">Customer Segments</h4>
                <div className="flex flex-wrap gap-2">
                  {overview.customerSegments.map((segment, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="bg-primary/10 text-primary border-primary/20"
                    >
                      {segment}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Competitive Advantages */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Trophy className="h-5 w-5 text-yellow-400" />
              </div>
              <AuthCardTitle>Competitive Advantages</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {overview.competitiveAdvantages.map((advantage, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-3 p-3 bg-yellow-500/5 border border-yellow-500/20 rounded-lg"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                >
                  <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0" />
                  <p className="text-sm text-muted-foreground">{advantage}</p>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
