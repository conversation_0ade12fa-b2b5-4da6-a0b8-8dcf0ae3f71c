"use client";
import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

interface ServiceCardProps {
  title: string;
  description: string;
  href: string;
  imageUrl: string;
  delay?: number;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  description,
  href,
  imageUrl,
  delay = 0,
}) => {
  // Card appearance variants with ultra-smooth transitions
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1.2, // Longer duration for smoother appearance
        ease: [0.22, 1, 0.36, 1], // Custom cubic-bezier for silky smooth motion
        delay
      }
    },
    hover: {
      y: -3, // Even more subtle lift
      boxShadow: "0 15px 30px rgba(0, 0, 0, 0.4)",
      borderColor: "rgba(173, 146, 238, 0.6)",
      transition: {
        duration: 0.7, // Slower transition for smoother effect
        ease: [0.34, 1.56, 0.64, 1] // Slight overshoot for organic feel
      }
    }
  };

  // Image animation variants with ultra-smooth zoom
  const imageVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.03, // Even more subtle zoom
      transition: {
        duration: 1.2, // Longer duration for smoother zoom
        ease: [0.16, 1, 0.3, 1] // Custom easing for silky smooth zoom
      }
    }
  };

  // Text animation variants with smoother motion
  const textVariants = {
    initial: { y: 0, opacity: 1 },
    hover: {
      y: -2, // Very subtle movement
      opacity: 1,
      transition: {
        duration: 0.8, // Longer duration
        ease: [0.34, 1.56, 0.64, 1] // Slight overshoot for organic feel
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      whileInView="visible"
      whileHover="hover"
      viewport={{ once: true, amount: 0.1 }} // Reduced threshold for better performance
      className="relative bg-[#201A2C] rounded-xl shadow-lg overflow-hidden
                 cursor-pointer border border-transparent transition-transform duration-300
                 flex flex-col h-full will-change-transform transform-gpu"
    >
      <Link href={href} className="flex flex-col h-full">
        {/* Image container */}
        <div className="relative overflow-hidden h-64">
          {/* Animated image with subtle zoom */}
          <motion.div
            variants={imageVariants}
            className="absolute inset-0 w-full h-full will-change-transform"
          >
            <Image
              src={imageUrl}
              alt={title}
              fill
              priority={delay < 0.15} // Prioritize first two images
              loading={delay < 0.15 ? "eager" : "lazy"}
              className="object-cover will-change-transform"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              quality={90} // Higher quality images
            />
          </motion.div>

          {/* Static gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-[#201A2C] via-transparent to-transparent opacity-60" />

          {/* Ultra-smooth shine effect */}
          <motion.div
            initial={{ opacity: 0, x: "-100%" }}
            animate={{
              opacity: 0.12, // Even more subtle opacity
              x: "100%",
              transition: {
                duration: 3, // Much slower animation for smoother effect
                ease: [0.22, 0.61, 0.36, 1], // Custom easing for silky smooth motion
                repeat: Infinity,
                repeatType: "loop" as const,
                repeatDelay: 3 // Longer delay for less frequent animation
              }
            }}
            className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent"
            style={{
              mixBlendMode: "overlay",
              transform: "skewX(30deg)", // Less extreme angle
              width: "150%", // Wider gradient for smoother effect
              left: "-25%"
            }}
          />

          {/* Title overlay at bottom of image */}
          <motion.div
            variants={textVariants}
            className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-[#201A2C] to-transparent"
          >
            <h3 className="text-xl font-semibold text-white">{title}</h3>
          </motion.div>
        </div>

        {/* Card content */}
        <div className="p-4 flex-grow flex flex-col justify-between">
          <p className="text-gray-300 text-sm leading-relaxed line-clamp-3">
            {description}
          </p>

          {/* Animated button/link with smoother transitions */}
          <motion.div
            className="mt-4 self-start"
            whileHover={{
              x: 3, // More subtle movement
              transition: {
                duration: 0.5,
                ease: [0.34, 1.56, 0.64, 1] // Smooth overshoot
              }
            }}
          >
            <motion.span
              className="text-primary text-sm font-medium flex items-center"
              whileHover={{ color: "hsl(var(--primary) / 1)" }}
              transition={{ duration: 0.3 }}
            >
              Learn more
              <motion.svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 ml-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                initial={{ x: 0 }}
                whileHover={{
                  x: 3,
                  transition: {
                    duration: 0.5,
                    ease: [0.34, 1.56, 0.64, 1],
                    repeat: Infinity,
                    repeatType: "mirror" as const,
                    repeatDelay: 0.3
                  }
                }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </motion.svg>
            </motion.span>
          </motion.div>
        </div>
      </Link>
    </motion.div>
  );
};

export default ServiceCard;
