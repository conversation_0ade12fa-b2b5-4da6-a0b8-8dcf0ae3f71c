'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { Button } from '@/components/ui/button';
import { MovingBorder } from '@/components/ui/aceternity/moving-border';
import { Card } from '@/components/ui/card';
import { Mail, MapPin, Phone } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';

export default function ContactPageContent() {
  const { t } = useLanguage();

  return (
    <>
      <AnimatedGradientBackground className="min-h-[40vh] flex flex-col items-center justify-center pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4"
            >
              {t('contact.getInTouch')}
            </motion.div>
            
            <AnimatedText
              text={t('contact.readyToTransform')}
              className="text-4xl md:text-5xl font-bold tracking-tight mb-6"
            />
            
            <AnimatedText
              text={t('contact.discussSolutions')}
              className="text-xl text-muted-foreground"
              once
            />
          </div>
        </div>
      </AnimatedGradientBackground>
      
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <TextReveal>
                <h2 className="text-3xl font-bold mb-6">{t('contact.information')}</h2>
                <p className="text-muted-foreground mb-8">
                  {t('contact.helpText')}
                </p>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <Mail className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">{t('contact.email')}</h3>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <Phone className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">{t('contact.phone')}</h3>
                      <p className="text-muted-foreground">+976 9988 7766</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <MapPin className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">{t('contact.office')}</h3>
                      <p className="text-muted-foreground">
                        123 Innovation Street<br />
                        Ulaanbaatar<br />
                        Mongolia
                      </p>
                    </div>
                  </div>
                </div>
              </TextReveal>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-8 border-primary/20">
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="text-sm font-medium">
                        {t('contact.name')}
                      </label>
                      <input
                        id="name"
                        type="text"
                        className="w-full px-4 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
                        placeholder={t('contact.yourName')}
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium">
                        {t('contact.email')}
                      </label>
                      <input
                        id="email"
                        type="email"
                        className="w-full px-4 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
                        placeholder={t('contact.yourEmail')}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="subject" className="text-sm font-medium">
                      {t('contact.subject')}
                    </label>
                    <input
                      id="subject"
                      type="text"
                      className="w-full px-4 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder={t('contact.howCanWeHelp')}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-medium">
                      {t('contact.message')}
                    </label>
                    <textarea
                      id="message"
                      rows={5}
                      className="w-full px-4 py-2 border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
                      placeholder={t('contact.tellUs')}
                    />
                  </div>
                  <div className="flex justify-center">
                    <MovingBorder className="p-0.5">
                      <Button size="lg" className="relative w-full md:w-auto px-8 py-6 text-base">
                        {t('contact.sendMessage')}
                      </Button>
                    </MovingBorder>
                  </div>
                </form>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
}
