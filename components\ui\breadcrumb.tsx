'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  return (
    <motion.nav
      className={cn("flex items-center space-x-2 text-sm", className)}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {items.map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )}
          
          {item.href ? (
            <Link
              href={item.href}
              className="flex items-center space-x-1 text-muted-foreground hover:text-primary transition-colors duration-200"
            >
              {item.icon && <span className="h-4 w-4">{item.icon}</span>}
              <span>{item.label}</span>
            </Link>
          ) : (
            <div className="flex items-center space-x-1 text-white font-medium">
              {item.icon && <span className="h-4 w-4">{item.icon}</span>}
              <span>{item.label}</span>
            </div>
          )}
        </div>
      ))}
    </motion.nav>
  );
}

// Predefined breadcrumb configurations for common pages
export const breadcrumbConfigs = {
  companyInstructions: (companyName: string) => [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Programs', href: '/programs' },
    { label: 'Portfolio', href: '/programs/portfolio' },
    { label: companyName }
  ],
  
  portfolio: () => [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Programs', href: '/programs' },
    { label: 'Portfolio' }
  ],
  
  successStories: () => [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Success Stories' }
  ],
  
  programs: () => [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Programs' }
  ]
};
