'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

export const MovingBorder = ({
  children,
  duration = 2000,
  rx = '30%',
  ry = '30%',
  className,
  containerClassName,
  borderClassName,
  ...props
}: {
  children: React.ReactNode;
  duration?: number;
  rx?: string;
  ry?: string;
  className?: string;
  containerClassName?: string;
  borderClassName?: string;
  [key: string]: any;
}) => {
  return (
    <div
      className={cn(
        'relative flex h-fit w-fit items-center justify-center',
        containerClassName
      )}
    >
      <div
        className={cn(
          'z-10 flex items-center justify-center',
          className
        )}
        {...props}
      >
        {children}
      </div>

      <motion.div
        className={cn(
          'absolute inset-0 z-[5] h-full w-full rounded-full border-2 border-primary/50 opacity-80',
          borderClassName
        )}
        style={{
          rx,
          ry,
        }}
        animate={{
          pathLength: [0.4, 0.5, 0.4],
          pathOffset: [0, 0.5, 1],
        }}
        transition={{
          duration: duration / 1000,
          ease: 'linear',
          repeat: Infinity,
          repeatType: 'loop',
        }}
      />
    </div>
  );
};
