// Company data structure for incubator participants

export interface Mentor {
  id: string;
  name: string;
  role: string;
  expertise: string[];
  image: string;
  bio: string;
  email: string;
  linkedin?: string;
  calendlyUrl?: string;
}

export interface Milestone {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'in-progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high';
  resources?: string[];
}

export interface ProgramTrack {
  id: string;
  name: string;
  description: string;
  duration: string;
  stage: string;
  color: string;
  icon: string;
}

export interface FounderProfile {
  id: string;
  name: string;
  role: string;
  photo: string;
  bio: string;
  education: string[];
  previousExperience: string[];
  expertise: string[];
  linkedin?: string;
  twitter?: string;
}

export interface ProductFeature {
  name: string;
  description: string;
  icon: string;
}

export interface CompanyTimeline {
  date: string;
  title: string;
  description: string;
  type: 'founding' | 'product' | 'funding' | 'milestone' | 'award' | 'media';
  icon: string;
}

export interface FundingRound {
  round: string;
  amount: string;
  date: string;
  investors: string[];
  valuation?: string;
  purpose: string;
}

export interface CompetitorAnalysis {
  name: string;
  description: string;
  strengths: string[];
  weaknesses: string[];
  marketShare?: string;
}

export interface Company {
  id: string;
  name: string;
  logo: string;
  description: string;
  industry: string;
  website?: string;
  foundedYear: number;
  founders: string[];
  programTrack: ProgramTrack;
  currentStage: string;
  joinDate: string;
  mentors: Mentor[];
  milestones: Milestone[];
  resources: {
    courses: string[];
    documents: string[];
    tools: string[];
  };
  contact: {
    primaryContact: string;
    email: string;
    phone?: string;
  };
  progress: {
    overall: number;
    milestones: number;
    courses: number;
  };
  nextSteps: string[];
  faq: {
    question: string;
    answer: string;
  }[];

  // Enhanced profile data
  profile: {
    overview: {
      foundingStory: string;
      mission: string;
      vision: string;
      coreValues: string[];
      businessModel: string;
      valueProposition: string;
      targetMarket: string;
      customerSegments: string[];
      competitiveAdvantages: string[];
    };

    foundersAndLeadership: {
      founders: FounderProfile[];
      leadership: FounderProfile[];
      advisors: FounderProfile[];
    };

    productService: {
      overview: string;
      features: ProductFeature[];
      technicalSpecs: string[];
      developmentTimeline: string;
      roadmap: string[];
      useCases: string[];
      testimonials: {
        customer: string;
        company: string;
        quote: string;
        role: string;
      }[];
    };

    marketAnalysis: {
      industryOverview: string;
      marketSize: string;
      targetDemographics: string[];
      competitors: CompetitorAnalysis[];
      marketPositioning: string;
      strategy: string[];
    };

    financial: {
      fundingHistory: FundingRound[];
      revenueModel: string;
      keyMetrics: {
        metric: string;
        value: string;
        description: string;
      }[];
      projections: string;
      investors: string[];
    };

    timeline: CompanyTimeline[];

    technology: {
      architecture: string;
      techStack: string[];
      intellectualProperty: string[];
      rdInitiatives: string[];
      partnerships: string[];
    };

    socialImpact: {
      environmentalImpact: string;
      sustainabilityEfforts: string[];
      socialResponsibility: string[];
      communityInvolvement: string[];
      esgCommitments: string[];
    };
  };
}

// Program tracks
export const programTracks: ProgramTrack[] = [
  {
    id: 'accelerator',
    name: 'Accelerator Program',
    description: '8-month intensive program for pre-seed and seed-stage startups',
    duration: '8 months',
    stage: 'Pre-seed to Seed',
    color: 'from-purple-500 to-blue-500',
    icon: '🚀'
  },
  {
    id: 'mongolian-idea',
    name: 'Mongolian Intellectual Idea',
    description: '3-month program for students and young entrepreneurs',
    duration: '3 months',
    stage: 'Idea to MVP',
    color: 'from-green-500 to-teal-500',
    icon: '💡'
  },
  {
    id: 'growth',
    name: 'Growth Program',
    description: '6-month program for scaling existing businesses',
    duration: '6 months',
    stage: 'Series A+',
    color: 'from-orange-500 to-red-500',
    icon: '📈'
  },
  {
    id: 'corporate',
    name: 'Corporate Innovation',
    description: '4-month program for corporate innovation projects',
    duration: '4 months',
    stage: 'Corporate',
    color: 'from-indigo-500 to-purple-500',
    icon: '🏢'
  }
];

// Mentors pool
export const mentorsPool: Mentor[] = [
  {
    id: 'mentor-1',
    name: 'Alexandra Chen',
    role: 'Business Strategy Mentor',
    expertise: ['Business Strategy', 'Fundraising', 'Market Expansion'],
    image: '/images/Team/team1.jpg',
    bio: 'Former VP at Goldman Sachs with 15+ years in venture capital and business strategy.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/alexandra-chen',
    calendlyUrl: 'https://calendly.com/alexandra-chen'
  },
  {
    id: 'mentor-2',
    name: 'Michael Rodriguez',
    role: 'Technology Advisor',
    expertise: ['Product Development', 'Technical Architecture', 'Team Building'],
    image: '/images/Team/team2.jpg',
    bio: 'Former CTO at multiple successful startups, expert in scaling technical teams.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/michael-rodriguez',
    calendlyUrl: 'https://calendly.com/michael-rodriguez'
  },
  {
    id: 'mentor-3',
    name: 'Sarah Johnson',
    role: 'Marketing & Growth Mentor',
    expertise: ['Digital Marketing', 'Growth Hacking', 'Brand Strategy'],
    image: '/images/Team/team3.jpg',
    bio: 'Former Head of Growth at unicorn startups, specialized in user acquisition.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/sarah-johnson',
    calendlyUrl: 'https://calendly.com/sarah-johnson'
  },
  {
    id: 'mentor-4',
    name: 'David Park',
    role: 'Operations Mentor',
    expertise: ['Operations', 'Process Optimization', 'Supply Chain'],
    image: '/images/Team/team4.jpg',
    bio: 'Operations expert who scaled multiple companies from startup to IPO.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/david-park',
    calendlyUrl: 'https://calendly.com/david-park'
  }
];

// Sample companies data
export const companies: Company[] = [
  {
    id: 'ecogrow',
    name: 'EcoGrow',
    logo: '/images/programs/1.jpg',
    description: 'Revolutionary smart farming solution using IoT and AI to optimize crop yields and reduce environmental impact.',
    industry: 'AgriTech',
    website: 'https://ecogrow.mn',
    foundedYear: 2023,
    founders: ['Batbayar Ganbold', 'Oyunaa Munkh'],
    programTrack: programTracks[0], // Accelerator Program
    currentStage: 'Seed Funding',
    joinDate: '2024-01-15',
    mentors: [mentorsPool[0], mentorsPool[1]], // Alexandra Chen, Michael Rodriguez
    milestones: [
      {
        id: 'milestone-1',
        title: 'Complete Market Research',
        description: 'Conduct comprehensive market analysis and competitor research',
        dueDate: '2024-02-15',
        status: 'completed',
        priority: 'high',
        resources: ['Market Research Template', 'Competitor Analysis Guide']
      },
      {
        id: 'milestone-2',
        title: 'Develop MVP',
        description: 'Build minimum viable product with core features',
        dueDate: '2024-04-01',
        status: 'in-progress',
        priority: 'high',
        resources: ['MVP Development Guide', 'Technical Architecture Template']
      },
      {
        id: 'milestone-3',
        title: 'Secure Seed Funding',
        description: 'Raise $500K seed round from investors',
        dueDate: '2024-06-30',
        status: 'pending',
        priority: 'medium',
        resources: ['Pitch Deck Template', 'Financial Model Template']
      }
    ],
    resources: {
      courses: ['Entrepreneurship Fundamentals', 'Product Development', 'Fundraising Masterclass'],
      documents: ['Business Plan Template', 'Legal Documents', 'Financial Models'],
      tools: ['Slack Workspace', 'Google Workspace', 'Notion Dashboard']
    },
    contact: {
      primaryContact: 'Batbayar Ganbold',
      email: '<EMAIL>',
      phone: '+976 9999 1234'
    },
    progress: {
      overall: 65,
      milestones: 70,
      courses: 60
    },
    nextSteps: [
      'Complete user testing for MVP',
      'Prepare pitch deck for investors',
      'Finalize legal structure',
      'Hire first employee'
    ],
    faq: [
      {
        question: 'How do I access my dedicated Slack workspace?',
        answer: 'You can access your Slack workspace through the link provided in your welcome email. If you need a new invitation, contact your mentor.'
      },
      {
        question: 'When are mentor meetings scheduled?',
        answer: 'Mentor meetings are typically scheduled bi-weekly. You can book sessions directly through your mentor\'s Calendly link.'
      },
      {
        question: 'What resources are available for fundraising?',
        answer: 'We provide pitch deck templates, financial model templates, investor databases, and direct introductions to our investor network.'
      }
    ],

    profile: {
      overview: {
        foundingStory: "EcoGrow was founded in 2023 by agricultural engineer Batbayar Ganbold and data scientist Oyunaa Munkh, who met during a sustainable agriculture conference in Ulaanbaatar. Frustrated by the inefficiencies in traditional farming methods and the environmental challenges facing Mongolia's agricultural sector, they envisioned a technology-driven solution that could optimize crop yields while reducing environmental impact.",
        mission: "To revolutionize agriculture through intelligent IoT solutions that maximize crop yields while minimizing environmental impact, making sustainable farming accessible and profitable for farmers worldwide.",
        vision: "To become the leading AgriTech platform in Central Asia, empowering farmers with data-driven insights and automated systems that create a more sustainable and productive agricultural ecosystem.",
        coreValues: [
          "Sustainability - Environmental stewardship in all our solutions",
          "Innovation - Cutting-edge technology for agricultural advancement",
          "Farmer-Centric - Solutions designed with farmers' needs at the center",
          "Data Integrity - Accurate, reliable data for informed decision-making",
          "Collaboration - Building partnerships across the agricultural value chain"
        ],
        businessModel: "B2B SaaS model with hardware-software integration. We provide IoT sensor packages, cloud-based analytics platform, and ongoing support services through subscription tiers based on farm size and feature requirements.",
        valueProposition: "EcoGrow combines IoT sensors, AI analytics, and automated irrigation systems to increase crop yields by 40% while reducing water usage by 30% and fertilizer costs by 25%, providing farmers with real-time insights and automated optimization.",
        targetMarket: "Commercial farms, agricultural cooperatives, and progressive individual farmers in Mongolia, Central Asia, and emerging markets with focus on grain, vegetable, and fruit production.",
        customerSegments: [
          "Large commercial farms (500+ hectares)",
          "Medium-scale farms (50-500 hectares)",
          "Agricultural cooperatives and collectives",
          "Greenhouse and controlled environment operations",
          "Government agricultural programs"
        ],
        competitiveAdvantages: [
          "Localized solutions for harsh climate conditions",
          "Integrated hardware-software approach",
          "AI models trained on regional agricultural data",
          "Cost-effective pricing for emerging markets",
          "Strong partnerships with local agricultural institutions"
        ]
      },

      foundersAndLeadership: {
        founders: [
          {
            id: 'batbayar-ganbold',
            name: 'Batbayar Ganbold',
            role: 'CEO & Co-Founder',
            photo: '/images/Team/team1.jpg',
            bio: 'Agricultural engineer with 8+ years of experience in sustainable farming practices. Led multiple agricultural modernization projects across Mongolia and has deep expertise in crop optimization and irrigation systems.',
            education: [
              'M.S. Agricultural Engineering - National University of Mongolia',
              'B.S. Agricultural Sciences - Mongolian University of Life Sciences'
            ],
            previousExperience: [
              'Senior Agricultural Consultant - Ministry of Agriculture (2018-2023)',
              'Project Manager - Sustainable Agriculture Initiative (2016-2018)',
              'Agricultural Engineer - Green Valley Farms (2015-2016)'
            ],
            expertise: ['Sustainable Agriculture', 'Irrigation Systems', 'Crop Optimization', 'Project Management'],
            linkedin: 'https://linkedin.com/in/batbayar-ganbold'
          },
          {
            id: 'oyunaa-munkh',
            name: 'Oyunaa Munkh',
            role: 'CTO & Co-Founder',
            photo: '/images/Team/team2.jpg',
            bio: 'Data scientist and software engineer specializing in IoT applications and machine learning. Former tech lead at multiple startups with expertise in building scalable data platforms and AI-driven analytics systems.',
            education: [
              'M.S. Computer Science - National University of Mongolia',
              'B.S. Mathematics & Computer Science - Mongolian University of Science and Technology'
            ],
            previousExperience: [
              'Senior Data Scientist - TechMongolia (2020-2023)',
              'Software Engineer - DataFlow Solutions (2018-2020)',
              'Junior Developer - Innovation Labs (2017-2018)'
            ],
            expertise: ['Machine Learning', 'IoT Development', 'Data Analytics', 'Cloud Architecture'],
            linkedin: 'https://linkedin.com/in/oyunaa-munkh'
          }
        ],
        leadership: [
          {
            id: 'ganbaatar-bold',
            name: 'Ganbaatar Bold',
            role: 'Head of Sales & Marketing',
            photo: '/images/Team/team3.jpg',
            bio: 'Marketing professional with extensive experience in agricultural sector sales and business development. Joined EcoGrow in early 2024 to lead market expansion efforts.',
            education: [
              'MBA - International Business School Mongolia',
              'B.S. Business Administration - National University of Mongolia'
            ],
            previousExperience: [
              'Sales Director - AgriSupply Mongolia (2019-2024)',
              'Business Development Manager - FarmTech Solutions (2017-2019)'
            ],
            expertise: ['Agricultural Sales', 'Market Development', 'Strategic Partnerships', 'Customer Relations'],
            linkedin: 'https://linkedin.com/in/ganbaatar-bold'
          }
        ],
        advisors: [
          {
            id: 'dr-sarah-chen',
            name: 'Dr. Sarah Chen',
            role: 'Agricultural Technology Advisor',
            photo: '/images/Team/team4.jpg',
            bio: 'Former Director of Agricultural Innovation at FAO with 20+ years of experience in sustainable agriculture and technology adoption in developing countries.',
            education: [
              'Ph.D. Agricultural Sciences - UC Davis',
              'M.S. Environmental Engineering - Stanford University'
            ],
            previousExperience: [
              'Director of Agricultural Innovation - FAO (2015-2022)',
              'Senior Research Scientist - International Rice Research Institute (2010-2015)'
            ],
            expertise: ['Sustainable Agriculture', 'Technology Adoption', 'International Development', 'Policy Advisory'],
            linkedin: 'https://linkedin.com/in/dr-sarah-chen'
          }
        ]
      },

      productService: {
        overview: "EcoGrow's smart farming solution combines IoT sensors, AI analytics, and automated irrigation systems to optimize agricultural productivity while promoting sustainable farming practices.",
        features: [
          { name: 'Smart Irrigation', description: 'Automated watering based on soil moisture and weather data', icon: '💧' },
          { name: 'Crop Monitoring', description: 'Real-time monitoring of crop health and growth patterns', icon: '🌱' },
          { name: 'Weather Integration', description: 'Advanced weather forecasting and climate adaptation', icon: '🌤️' },
          { name: 'Data Analytics', description: 'AI-powered insights and predictive analytics dashboard', icon: '📊' }
        ],
        technicalSpecs: [
          'LoRaWAN connectivity for long-range communication',
          'Solar-powered sensors with 5-year battery life',
          'IP67 waterproof rating for harsh weather conditions',
          'Real-time data processing with edge computing'
        ],
        developmentTimeline: "18-month development cycle from prototype to commercial launch",
        roadmap: [
          'Q3 2024: Drone integration for aerial monitoring',
          'Q4 2024: Mobile app 2.0 with enhanced UI/UX',
          'Q1 2025: AI-powered pest detection system',
          'Q2 2025: Blockchain integration for supply chain tracking'
        ],
        useCases: [
          'Large-scale grain production optimization',
          'Greenhouse vegetable farming automation',
          'Fruit orchard management and monitoring',
          'Sustainable farming practice implementation'
        ],
        testimonials: [
          {
            customer: 'Munkh-Erdene',
            company: 'Golden Valley Farms',
            quote: 'EcoGrow has transformed our farming operations. We\'ve seen a 35% increase in yield while reducing water usage significantly.',
            role: 'Farm Manager'
          }
        ]
      },

      marketAnalysis: {
        industryOverview: "The AgriTech market in Central Asia is experiencing rapid growth driven by increasing demand for food security, climate change challenges, and government initiatives promoting agricultural modernization.",
        marketSize: "$2.5B total addressable market in Central Asia with 15% annual growth rate",
        targetDemographics: [
          'Commercial farms (500+ hectares)',
          'Medium-scale farms (50-500 hectares)',
          'Agricultural cooperatives',
          'Government agricultural programs'
        ],
        competitors: [
          {
            name: 'Traditional Farming Methods',
            description: 'Manual farming practices without technology integration',
            strengths: ['Low initial cost', 'Familiar to farmers'],
            weaknesses: ['Inefficient resource usage', 'Lower yields', 'Labor intensive'],
            marketShare: '70%'
          }
        ],
        marketPositioning: "Leading localized AgriTech solution for harsh climate conditions",
        strategy: [
          'Direct sales to large commercial farms',
          'Partnerships with agricultural cooperatives',
          'Government pilot programs',
          'Trade show and conference presence'
        ]
      },

      financial: {
        fundingHistory: [
          {
            round: 'Pre-seed',
            amount: '$100K',
            date: '2023-09',
            investors: ['Angel Investors', 'InnoHub Pre-seed Fund'],
            purpose: 'Product development and initial market validation'
          }
        ],
        revenueModel: "Subscription-based SaaS model with tiered pricing based on farm size and feature requirements",
        keyMetrics: [
          { metric: 'Monthly Recurring Revenue', value: '$15K', description: 'Current MRR from pilot customers' },
          { metric: 'Customer Acquisition Cost', value: '$500', description: 'Average cost to acquire new customer' },
          { metric: 'Customer Lifetime Value', value: '$5,000', description: 'Projected LTV over 3 years' }
        ],
        projections: "Targeting $500K ARR by end of 2024 with 100+ customers",
        investors: ['InnoHub Ventures', 'AgriTech Angels', 'Sustainable Future Fund']
      },

      timeline: [
        { date: '2023-01', title: 'Company Founded', description: 'EcoGrow officially founded by Batbayar and Oyunaa', type: 'founding', icon: '🚀' },
        { date: '2023-03', title: 'First Prototype', description: 'Completed initial IoT sensor prototype and testing', type: 'product', icon: '🔧' },
        { date: '2023-06', title: 'Pilot Program', description: 'Launched pilot program with 5 local farms', type: 'milestone', icon: '🌱' },
        { date: '2023-09', title: 'Pre-seed Funding', description: 'Raised $100K in pre-seed funding', type: 'funding', icon: '💰' },
        { date: '2024-01', title: 'InnoHub Accelerator', description: 'Accepted into InnoHub Accelerator Program', type: 'milestone', icon: '🎯' },
        { date: '2024-03', title: 'Product Launch', description: 'Official product launch with 20 commercial customers', type: 'product', icon: '🚀' }
      ],

      technology: {
        architecture: "Cloud-native architecture with edge computing capabilities for real-time data processing and decision making",
        techStack: ['Node.js', 'MongoDB', 'AWS IoT Core', 'TensorFlow', 'React Native'],
        intellectualProperty: [
          'Smart irrigation algorithm (Patent Pending)',
          'Multi-sensor data fusion technology (Trade Secret)'
        ],
        rdInitiatives: [
          'AI-powered crop health prediction',
          'Drone integration for aerial monitoring',
          'Blockchain for supply chain tracking'
        ],
        partnerships: [
          'AWS IoT Partner Program',
          'MongoDB Startup Program',
          'National University of Mongolia Research Collaboration'
        ]
      },

      socialImpact: {
        environmentalImpact: "Reducing agricultural water usage by 30% and chemical fertilizer usage by 25% through precision farming techniques",
        sustainabilityEfforts: [
          'Solar-powered IoT devices',
          'Water conservation programs',
          'Reduced chemical usage',
          'Carbon footprint tracking'
        ],
        socialResponsibility: [
          'Training programs for smallholder farmers',
          'Affordable pricing for developing communities',
          'Local employment creation',
          'Knowledge sharing initiatives'
        ],
        communityInvolvement: [
          'Partnership with agricultural universities',
          'Rural development programs',
          'Farmer education initiatives'
        ],
        esgCommitments: [
          'Carbon neutral operations by 2025',
          '50% women in leadership by 2026',
          'Transparent reporting standards',
          'Ethical business practices'
        ]
      }
    }
  },
  {
    id: 'finedge',
    name: 'FinEdge',
    logo: '/images/programs/2.jpg',
    description: 'Next-generation digital banking platform providing financial services to underserved communities.',
    industry: 'FinTech',
    website: 'https://finedge.mn',
    foundedYear: 2023,
    founders: ['Munkhjargal Bold', 'Temuulen Bat'],
    programTrack: programTracks[0], // Accelerator Program
    currentStage: 'Pre-Seed',
    joinDate: '2024-02-01',
    mentors: [mentorsPool[0], mentorsPool[3]], // Alexandra Chen, David Park
    milestones: [
      {
        id: 'milestone-1',
        title: 'Regulatory Compliance Research',
        description: 'Research banking regulations and compliance requirements',
        dueDate: '2024-03-15',
        status: 'completed',
        priority: 'high',
        resources: ['Regulatory Guide', 'Compliance Checklist']
      },
      {
        id: 'milestone-2',
        title: 'Build Core Banking Features',
        description: 'Develop core banking functionality and security features',
        dueDate: '2024-05-01',
        status: 'in-progress',
        priority: 'high',
        resources: ['Banking API Documentation', 'Security Best Practices']
      }
    ],
    resources: {
      courses: ['FinTech Fundamentals', 'Regulatory Compliance', 'Digital Banking'],
      documents: ['Banking License Guide', 'Security Protocols', 'API Documentation'],
      tools: ['Slack Workspace', 'GitHub Enterprise', 'AWS Credits']
    },
    contact: {
      primaryContact: 'Munkhjargal Bold',
      email: '<EMAIL>',
      phone: '+976 9999 5678'
    },
    progress: {
      overall: 45,
      milestones: 50,
      courses: 40
    },
    nextSteps: [
      'Complete regulatory approval process',
      'Implement security protocols',
      'Conduct user testing',
      'Prepare for pre-seed funding'
    ],
    faq: [
      {
        question: 'What regulatory requirements do we need to meet?',
        answer: 'You need to comply with banking regulations, data protection laws, and financial services licensing. Our legal team can provide detailed guidance.'
      },
      {
        question: 'How can we access banking APIs for testing?',
        answer: 'We provide sandbox access to banking APIs through our partner network. Contact your technical mentor for setup instructions.'
      }
    ]
  },
  {
    id: 'healthtech-pro',
    name: 'HealthTech Pro',
    logo: '/images/programs/plant-bulbs.jpg',
    description: 'AI-powered healthcare platform connecting patients with medical professionals through telemedicine.',
    industry: 'HealthTech',
    foundedYear: 2024,
    founders: ['Dr. Oyungerel Dash', 'Ganbaatar Tsedev'],
    programTrack: programTracks[1], // Mongolian Intellectual Idea
    currentStage: 'MVP Development',
    joinDate: '2024-03-01',
    mentors: [mentorsPool[1], mentorsPool[2]], // Michael Rodriguez, Sarah Johnson
    milestones: [
      {
        id: 'milestone-1',
        title: 'Healthcare Market Analysis',
        description: 'Analyze healthcare market opportunities and challenges',
        dueDate: '2024-03-30',
        status: 'completed',
        priority: 'medium',
        resources: ['Healthcare Market Report', 'Telemedicine Trends']
      },
      {
        id: 'milestone-2',
        title: 'MVP Development',
        description: 'Build basic telemedicine platform with video consultation',
        dueDate: '2024-05-15',
        status: 'in-progress',
        priority: 'high',
        resources: ['Healthcare App Development Guide', 'HIPAA Compliance']
      }
    ],
    resources: {
      courses: ['Healthcare Innovation', 'AI in Medicine', 'Telemedicine Basics'],
      documents: ['HIPAA Compliance Guide', 'Medical Device Regulations', 'Privacy Policies'],
      tools: ['Slack Workspace', 'Figma Pro', 'AWS Healthcare Credits']
    },
    contact: {
      primaryContact: 'Dr. Oyungerel Dash',
      email: '<EMAIL>',
      phone: '+976 9999 9012'
    },
    progress: {
      overall: 35,
      milestones: 40,
      courses: 30
    },
    nextSteps: [
      'Complete MVP development',
      'Obtain medical device certification',
      'Conduct pilot testing with clinics',
      'Develop go-to-market strategy'
    ],
    faq: [
      {
        question: 'What medical certifications do we need?',
        answer: 'You need medical device certification and healthcare data compliance. Our healthcare advisors can guide you through the process.'
      },
      {
        question: 'How can we find pilot customers?',
        answer: 'We have partnerships with local clinics and hospitals. Your mentor can facilitate introductions for pilot testing.'
      }
    ]
  }
];
