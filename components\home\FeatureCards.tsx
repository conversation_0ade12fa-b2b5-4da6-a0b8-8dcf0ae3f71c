'use client';

import React from 'react';
import { motion } from 'framer-motion';

export default function FeatureCards() {
  const features = [
    {
      icon: "🚀",
      title: "Accelerated Growth",
      description: "Fast-track your startup journey with our proven methodologies and expert guidance.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: "💡",
      title: "Innovation Hub",
      description: "Access cutting-edge resources and collaborate with like-minded entrepreneurs.",
      gradient: "from-blue-500 to-purple-500"
    },
    {
      icon: "🌟",
      title: "Success Network",
      description: "Connect with investors, mentors, and successful entrepreneurs in our ecosystem.",
      gradient: "from-pink-500 to-orange-500"
    }
  ];

  return (
    <div className="relative py-32 bg-black text-white overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-950/10 to-black"></div>

      {/* Animated geometric shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/4 left-1/4 w-32 h-32 border border-purple-500/20 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-24 h-24 border border-pink-500/20 rounded-lg"
          animate={{ rotate: -360 }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-indigo-500/10 rounded-full"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-block mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ duration: 1, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="h-0.5 bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400 bg-clip-text text-transparent">
            Entrepreneurial Success
          </h2>

          <motion.p
            className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            We provide the resources, expertise, and environment needed to transform innovative ideas into thriving businesses. Our incubator is designed to accelerate your entrepreneurial journey.
          </motion.p>
        </motion.div>

        {/* Enhanced Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              className="group relative"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.8,
                delay: index * 0.2,
                ease: [0.22, 1, 0.36, 1]
              }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ y: -10 }}
            >
              {/* Card Background with Gradient Border */}
              <div className="relative h-full p-8 rounded-2xl bg-gradient-to-b from-gray-900/50 to-black/50 backdrop-blur-sm border border-gray-800/50 group-hover:border-purple-500/50 transition-all duration-500">
                {/* Gradient Border Effect */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-xl`}></div>

                {/* Content */}
                <div className="relative z-10">
                  {/* Icon */}
                  <motion.div
                    className="text-6xl mb-6 text-center"
                    whileHover={{ scale: 1.2, rotate: 10 }}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  >
                    {feature.icon}
                  </motion.div>

                  {/* Title */}
                  <h3 className={`text-2xl font-bold mb-4 text-center bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}>
                    {feature.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-300 text-center leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Effect Line */}
                  <motion.div
                    className={`mt-6 h-0.5 bg-gradient-to-r ${feature.gradient} mx-auto`}
                    initial={{ width: 0 }}
                    whileInView={{ width: 60 }}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.6 }}
                    viewport={{ once: true }}
                  />
                </div>

                {/* Animated Background Particles */}
                <div className="absolute inset-0 overflow-hidden rounded-2xl">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
                      style={{
                        left: Math.random() * 100 + '%',
                        top: Math.random() * 100 + '%',
                      }}
                      animate={{
                        y: [0, -20, 0],
                        opacity: [0, 1, 0],
                      }}
                      transition={{
                        duration: 3 + Math.random() * 2,
                        repeat: Infinity,
                        delay: Math.random() * 2,
                      }}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
