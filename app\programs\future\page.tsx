'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { ArrowLeft } from 'lucide-react';

export default function FutureProgramPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative h-[60vh] overflow-hidden bg-black">
        {/* Background Image */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-70"
            style={{
              backgroundImage: 'url(/images/programs/plant-bulbs.jpg)',
              backgroundPosition: 'center center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black" />
        </div>

        {/* Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4">
          <Link 
            href="/programs" 
            className="absolute top-8 left-8 text-white/80 hover:text-white flex items-center transition-colors"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Буцах
          </Link>
          
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 max-w-4xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            ИРЭЭДҮЙ ХӨТӨЛБӨР
          </motion.h1>

          <motion.div
            className="w-24 h-1 bg-primary mb-8"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.5 }}
          />

          <motion.p
            className="text-xl text-white/80 max-w-2xl mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            Ахлах, дунд, сургуудад дэмжлэг үзүүлэх шийдвэрлэхэд дэмжлэг үзүүлэх 3 сарын онлайн хөтөлбөр.
          </motion.p>
        </div>
      </section>

      {/* Program Details Section */}
      <BokehBackground
        className="py-24"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
        density={60}
        speed={1.5}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              className="bg-black/30 backdrop-blur-md rounded-xl border border-white/10 p-8 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">Хөтөлбөрийн тухай</h2>
              <p className="text-white/80 mb-6">
                Ирээдүй хөтөлбөр нь ахлах, дунд сургуулийн сурагчдад зориулсан 3 сарын онлайн хөтөлбөр юм. Энэхүү хөтөлбөр нь залуу үеийнхэнд инновацийн сэтгэлгээг хөгжүүлэх, асуудал шийдвэрлэх чадварыг сайжруулах, технологийн мэдлэг олгоход чиглэгдсэн.
              </p>
              <p className="text-white/80 mb-6">
                Хөтөлбөрт хамрагдсанаар та дараах боломжуудыг авна:
              </p>
              <ul className="list-disc pl-6 text-white/80 space-y-2 mb-6">
                <li>Инновацийн сэтгэлгээгээ хөгжүүлэх</li>
                <li>Асуудал шийдвэрлэх чадвараа сайжруулах</li>
                <li>Технологийн мэдлэг эзэмших</li>
                <li>Багаар ажиллах чадвараа сайжруулах</li>
                <li>Төслөө танилцуулах, илтгэх чадвараа хөгжүүлэх</li>
                <li>Ирээдүйн мэргэжлээ сонгоход туслах</li>
              </ul>
              <p className="text-white/80">
                Хөтөлбөрт хамрагдахын тулд та дараах шаардлагуудыг хангасан байх шаардлагатай:
              </p>
              <ul className="list-disc pl-6 text-white/80 space-y-2 mt-4">
                <li>Ахлах, дунд сургуулийн сурагч байх</li>
                <li>Инновацийн сэтгэлгээ, технологид сонирхолтой байх</li>
                <li>Хөтөлбөрийн хугацаанд идэвхтэй оролцох боломжтой байх</li>
              </ul>
            </motion.div>

            <motion.div
              className="text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">Хөтөлбөрт бүртгүүлэх</h2>
              <p className="text-white/80 mb-8 max-w-2xl mx-auto">
                Хөтөлбөрт бүртгүүлэхийн тулд доорх товчийг дарж өргөдлөө бөглөнө үү. Бүртгэл 2024 оны 8-р сарын 1-нд хаагдана.
              </p>
              <motion.button
                className="px-8 py-3 bg-primary text-white rounded-full font-medium hover:bg-primary/90 transition-colors duration-300 shadow-lg shadow-primary/20"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
              >
                Бүртгүүлэх
              </motion.button>
            </motion.div>
          </div>
        </div>
      </BokehBackground>
    </>
  );
}
