import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Handle courses routing
  if (pathname === '/courses') {
    // Check if user is authenticated
    const authToken = request.cookies.get('innohub_auth')?.value;

    if (!authToken || authToken !== 'true') {
      // Redirect unauthenticated users to welcome page
      return NextResponse.redirect(new URL('/courses/welcome', request.url));
    }

    // Check if user has completed onboarding
    const userOnboarded = request.cookies.get('userOnboarded')?.value;

    if (!userOnboarded || userOnboarded !== 'true') {
      // Redirect to onboarding if not completed
      return NextResponse.redirect(new URL('/onboarding', request.url));
    }

    // User is authenticated and onboarded, redirect to dashboard
    return NextResponse.redirect(new URL('/courses/dashboard', request.url));
  }

  // Handle individual course pages
  if (pathname.startsWith('/courses/') && pathname !== '/courses/welcome') {
    const authToken = request.cookies.get('innohub_auth')?.value;

    if (!authToken || authToken !== 'true') {
      // Redirect to login with return URL
      const redirectUrl = encodeURIComponent(pathname);
      return NextResponse.redirect(new URL(`/auth/login?redirect=${redirectUrl}`, request.url));
    }

    const userOnboarded = request.cookies.get('userOnboarded')?.value;

    if (!userOnboarded || userOnboarded !== 'true') {
      return NextResponse.redirect(new URL('/onboarding', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/courses/:path*',
    '/onboarding/:path*',
    '/auth/:path*'
  ]
};
